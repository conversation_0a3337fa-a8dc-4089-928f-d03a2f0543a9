Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SupportApps", "SupportApps", "{50A473A1-3642-20B3-630E-B0C6E43AE01B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Pos.net", "Pos.net", "{98AABB35-1B25-682F-E6A7-F2372B29BCBE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PosFor.Net", "PosFor.Net", "{46A876AE-C72E-0372-3249-0E941BF29799}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Files", "Files", "{6320298C-C1C4-AF98-CB59-28D3233CC82C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SDK", "SDK", "{2E728E98-0088-7A3E-A53E-B127BA26603B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Samples", "Samples", "{67C4B8D1-BD50-933B-D1F0-DF58ABAFBF36}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Example Service Objects", "Example Service Objects", "{9557F8C2-A449-2A4C-B14A-723FD0B2D88B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExampleServiceObjects", "SupportApps\Pos.net\PosFor.Net\Files\SDK\Samples\Example Service Objects\Source\ExampleServiceObjects.csproj", "{9859D440-022A-F08D-E1A8-BDAD3B9E362E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Sample Application", "Sample Application", "{822A3115-F084-EDB2-74E5-CE26679723FE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SampleApplication", "SupportApps\Pos.net\PosFor.Net\Files\SDK\Samples\Sample Application\Source\SampleApplication.csproj", "{6B1729CB-BE03-326B-062D-3F4E0C0C41F2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Simulator Service Objects", "Simulator Service Objects", "{FA75109D-7B43-0455-59F4-7E49F236E8E4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Simulators", "SupportApps\Pos.net\PosFor.Net\Files\SDK\Samples\Simulator Service Objects\Source\Simulators.csproj", "{B30AF847-BF65-4533-629F-49A9FDEB085A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9859D440-022A-F08D-E1A8-BDAD3B9E362E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9859D440-022A-F08D-E1A8-BDAD3B9E362E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9859D440-022A-F08D-E1A8-BDAD3B9E362E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9859D440-022A-F08D-E1A8-BDAD3B9E362E}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B1729CB-BE03-326B-062D-3F4E0C0C41F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B1729CB-BE03-326B-062D-3F4E0C0C41F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B1729CB-BE03-326B-062D-3F4E0C0C41F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B1729CB-BE03-326B-062D-3F4E0C0C41F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{B30AF847-BF65-4533-629F-49A9FDEB085A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B30AF847-BF65-4533-629F-49A9FDEB085A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B30AF847-BF65-4533-629F-49A9FDEB085A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B30AF847-BF65-4533-629F-49A9FDEB085A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{98AABB35-1B25-682F-E6A7-F2372B29BCBE} = {50A473A1-3642-20B3-630E-B0C6E43AE01B}
		{46A876AE-C72E-0372-3249-0E941BF29799} = {98AABB35-1B25-682F-E6A7-F2372B29BCBE}
		{6320298C-C1C4-AF98-CB59-28D3233CC82C} = {46A876AE-C72E-0372-3249-0E941BF29799}
		{2E728E98-0088-7A3E-A53E-B127BA26603B} = {6320298C-C1C4-AF98-CB59-28D3233CC82C}
		{67C4B8D1-BD50-933B-D1F0-DF58ABAFBF36} = {2E728E98-0088-7A3E-A53E-B127BA26603B}
		{9557F8C2-A449-2A4C-B14A-723FD0B2D88B} = {67C4B8D1-BD50-933B-D1F0-DF58ABAFBF36}
		{9859D440-022A-F08D-E1A8-BDAD3B9E362E} = {9557F8C2-A449-2A4C-B14A-723FD0B2D88B}
		{822A3115-F084-EDB2-74E5-CE26679723FE} = {67C4B8D1-BD50-933B-D1F0-DF58ABAFBF36}
		{6B1729CB-BE03-326B-062D-3F4E0C0C41F2} = {822A3115-F084-EDB2-74E5-CE26679723FE}
		{FA75109D-7B43-0455-59F4-7E49F236E8E4} = {67C4B8D1-BD50-933B-D1F0-DF58ABAFBF36}
		{B30AF847-BF65-4533-629F-49A9FDEB085A} = {FA75109D-7B43-0455-59F4-7E49F236E8E4}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B2AD3077-327B-4B02-BEB6-355718F2C39D}
	EndGlobalSection
EndGlobal
