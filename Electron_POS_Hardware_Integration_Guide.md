# Electron.js POS Hardware Integration Guide

## Overview

This comprehensive guide covers migrating your existing C# POS system to Electron.js while maintaining the same hardware functionality and interfaces. The current system uses Microsoft POS for .NET with OPOS drivers, and this guide provides equivalent Electron.js implementations.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Hardware Components](#hardware-components)
3. [USB Camera Integration](#usb-camera-integration)
4. [Barcode Scanner Integration](#barcode-scanner-integration)
5. [Cash Drawer Integration](#cash-drawer-integration)
6. [Receipt Printer Integration](#receipt-printer-integration)
7. [Barcode Discount System](#barcode-discount-system)
8. [Installation & Setup](#installation--setup)
9. [Configuration](#configuration)
10. [Troubleshooting](#troubleshooting)

## System Architecture

### Current C# Architecture
- **Framework**: Microsoft POS for .NET v1.14
- **Hardware Layer**: OPOS (OLE for Retail POS)
- **Communication**: USB HID, Serial, OPOS drivers
- **Libraries**: Microsoft.PointOfService.dll, DynamicDotNetTWAIN.dll

### Electron.js Architecture
```
┌─────────────────────────────────────┐
│           Electron Main Process     │
├─────────────────────────────────────┤
│  Hardware Abstraction Layer (HAL)   │
├─────────────────────────────────────┤
│  USB HID │ Serial │ Camera │ Network │
├─────────────────────────────────────┤
│ Scanner │ Printer │ Drawer │ Camera  │
└─────────────────────────────────────┘
```

## Hardware Components

### Required Node.js Packages
```bash
npm install --save \
  node-hid \
  serialport \
  escpos \
  escpos-usb \
  escpos-serialport \
  node-webcam \
  quagga \
  sqlite3 \
  usb-detection
```

### Hardware Connection Types

| Device Type | Connection | Port/Interface | Driver Required |
|-------------|------------|----------------|-----------------|
| Barcode Scanner | USB HID | USB Port | HID Driver |
| Receipt Printer | USB/Serial | USB/COM Port | ESC/POS |
| Cash Drawer | Serial/USB | COM Port/USB | Serial Driver |
| USB Camera | USB | USB Port | UVC Driver |
| Line Display | USB/Serial | USB/COM Port | USB/Serial |

## USB Camera Integration

### Implementation Options

#### Option 1: Web APIs (Recommended)
```javascript
// camera-manager.js
class CameraManager {
  constructor() {
    this.stream = null;
    this.isActive = false;
  }

  async initialize() {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'environment'
        }
      });
      
      const videoElement = document.getElementById('camera-preview');
      videoElement.srcObject = this.stream;
      this.isActive = true;
      return true;
    } catch (error) {
      console.error('Camera initialization failed:', error);
      return false;
    }
  }

  captureImage() {
    if (!this.isActive) throw new Error('Camera not initialized');
    
    const canvas = document.createElement('canvas');
    const video = document.querySelector('video');
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);
    
    return canvas.toDataURL('image/jpeg', 0.8);
  }

  stop() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.isActive = false;
    }
  }
}
```

#### Option 2: Node.js Library
```javascript
// node-camera.js
const NodeWebcam = require('node-webcam');

class NodeCamera {
  constructor() {
    this.webcam = NodeWebcam.create({
      width: 1280,
      height: 720,
      quality: 100,
      delay: 0,
      saveShots: true,
      output: "jpeg",
      device: false,
      callbackReturn: "location",
      verbose: false
    });
  }

  async capture(filename) {
    return new Promise((resolve, reject) => {
      this.webcam.capture(filename, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
  }
}
```

## Barcode Scanner Integration

### USB HID Scanner
```javascript
// barcode-scanner.js
const HID = require('node-hid');
const usbDetection = require('usb-detection');

class BarcodeScanner {
  constructor() {
    this.device = null;
    this.isConnected = false;
    this.onDataCallback = null;
  }

  async connect(vendorId, productId) {
    try {
      const devices = HID.devices();
      const scanner = devices.find(d => 
        d.vendorId === vendorId && d.productId === productId
      );
      
      if (scanner) {
        this.device = new HID.HID(scanner.path);
        this.isConnected = true;
        this.setupDataListener();
        console.log('Barcode scanner connected');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Scanner connection failed:', error);
      return false;
    }
  }

  setupDataListener() {
    this.device.on('data', (data) => {
      const barcode = this.parseBarcode(data);
      if (barcode && this.onDataCallback) {
        this.onDataCallback(barcode);
      }
    });

    this.device.on('error', (error) => {
      console.error('Scanner error:', error);
      this.isConnected = false;
    });
  }

  parseBarcode(data) {
    let barcode = '';
    for (let i = 0; i < data.length; i++) {
      if (data[i] > 0 && data[i] < 255) {
        barcode += String.fromCharCode(data[i]);
      }
    }
    return barcode.trim();
  }

  onData(callback) {
    this.onDataCallback = callback;
  }

  disconnect() {
    if (this.device) {
      this.device.close();
      this.isConnected = false;
    }
  }
}
```

### Serial Scanner
```javascript
// serial-scanner.js
const { SerialPort } = require('serialport');
const { ReadlineParser } = require('@serialport/parser-readline');

class SerialBarcodeScanner {
  constructor(portPath, baudRate = 9600) {
    this.port = new SerialPort({
      path: portPath,
      baudRate: baudRate,
      dataBits: 8,
      parity: 'none',
      stopBits: 1
    });

    this.parser = this.port.pipe(new ReadlineParser({ delimiter: '\r\n' }));
    this.setupListeners();
  }

  setupListeners() {
    this.parser.on('data', (data) => {
      const barcode = data.toString().trim();
      if (barcode && this.onDataCallback) {
        this.onDataCallback(barcode);
      }
    });

    this.port.on('error', (error) => {
      console.error('Serial scanner error:', error);
    });
  }

  onData(callback) {
    this.onDataCallback = callback;
  }
}
```

### Camera-based Scanning
```javascript
// camera-scanner.js
const Quagga = require('quagga');

class CameraBarcodeScanner {
  constructor() {
    this.isScanning = false;
  }

  initialize() {
    return new Promise((resolve, reject) => {
      Quagga.init({
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: document.querySelector('#camera-scanner'),
          constraints: {
            width: 640,
            height: 480,
            facingMode: "environment"
          }
        },
        decoder: {
          readers: [
            "code_128_reader",
            "ean_reader",
            "ean_8_reader",
            "code_39_reader",
            "codabar_reader",
            "upc_reader",
            "upc_e_reader"
          ]
        }
      }, (err) => {
        if (err) {
          reject(err);
          return;
        }
        Quagga.start();
        this.isScanning = true;
        resolve();
      });
    });
  }

  onDetected(callback) {
    Quagga.onDetected((data) => {
      const barcode = data.codeResult.code;
      callback(barcode);
    });
  }

  stop() {
    if (this.isScanning) {
      Quagga.stop();
      this.isScanning = false;
    }
  }
}
```

## Cash Drawer Integration

### Serial Communication
```javascript
// cash-drawer.js
const { SerialPort } = require('serialport');

class CashDrawer {
  constructor(portPath, baudRate = 9600) {
    this.port = new SerialPort({
      path: portPath,
      baudRate: baudRate,
      dataBits: 8,
      parity: 'none',
      stopBits: 1
    });

    this.isOpen = false;
    this.setupListeners();
  }

  setupListeners() {
    this.port.on('open', () => {
      console.log('Cash drawer port opened');
      this.isOpen = true;
    });

    this.port.on('error', (error) => {
      console.error('Cash drawer error:', error);
      this.isOpen = false;
    });
  }

  openDrawer() {
    return new Promise((resolve, reject) => {
      if (!this.isOpen) {
        reject(new Error('Cash drawer port not open'));
        return;
      }

      // ESC/POS command: ESC p m t1 t2
      const openCommand = Buffer.from([0x1B, 0x70, 0x00, 0x19, 0x19]);
      
      this.port.write(openCommand, (error) => {
        if (error) {
          reject(error);
        } else {
          console.log('Cash drawer opened');
          resolve();
        }
      });
    });
  }

  checkDrawerStatus() {
    return new Promise((resolve, reject) => {
      const statusCommand = Buffer.from([0x1B, 0x75, 0x00]);
      
      this.port.write(statusCommand, (error) => {
        if (error) reject(error);
      });

      this.port.once('data', (data) => {
        const isOpen = (data[0] & 0x01) === 0;
        resolve(isOpen);
      });
    });
  }

  close() {
    if (this.port && this.port.isOpen) {
      this.port.close();
    }
  }
}
```

### USB Cash Drawer
```javascript
// usb-cash-drawer.js
const HID = require('node-hid');

class USBCashDrawer {
  constructor(vendorId, productId) {
    this.vendorId = vendorId;
    this.productId = productId;
    this.device = null;
  }

  connect() {
    try {
      const devices = HID.devices();
      const drawer = devices.find(d => 
        d.vendorId === this.vendorId && d.productId === this.productId
      );
      
      if (drawer) {
        this.device = new HID.HID(drawer.path);
        console.log('USB cash drawer connected');
        return true;
      }
      return false;
    } catch (error) {
      console.error('USB drawer connection failed:', error);
      return false;
    }
  }

  openDrawer() {
    if (!this.device) {
      throw new Error('Cash drawer not connected');
    }

    const openCommand = [0x00, 0x01, 0x00, 0x00];
    this.device.write(openCommand);
  }

  disconnect() {
    if (this.device) {
      this.device.close();
      this.device = null;
    }
  }
}
```

## Receipt Printer Integration

### ESC/POS Thermal Printer
```javascript
// thermal-printer.js
const escpos = require('escpos');
escpos.USB = require('escpos-usb');
escpos.Serial = require('escpos-serialport');

class ThermalPrinter {
  constructor(connectionType = 'usb', options = {}) {
    this.connectionType = connectionType;
    this.options = options;
    this.device = null;
    this.printer = null;
  }

  async connect() {
    try {
      if (this.connectionType === 'usb') {
        this.device = new escpos.USB(
          this.options.vendorId || 0x04b8, // Epson vendor ID
          this.options.productId || 0x0202  // Product ID
        );
      } else if (this.connectionType === 'serial') {
        this.device = new escpos.Serial(
          this.options.port || 'COM1',
          {
            baudRate: this.options.baudRate || 9600,
            dataBits: 8,
            stopBits: 1,
            parity: 'none'
          }
        );
      }

      this.printer = new escpos.Printer(this.device);

      return new Promise((resolve, reject) => {
        this.device.open((error) => {
          if (error) {
            reject(error);
          } else {
            console.log('Thermal printer connected');
            resolve();
          }
        });
      });
    } catch (error) {
      console.error('Printer connection failed:', error);
      throw error;
    }
  }

  async printReceipt(receiptData) {
    if (!this.printer) {
      throw new Error('Printer not connected');
    }

    return new Promise((resolve, reject) => {
      try {
        this.printer
          .font('a')
          .align('ct')
          .style('bu')
          .size(1, 1)
          .text(receiptData.storeName || 'POS SYSTEM')
          .text('--------------------------------')
          .align('lt')
          .style('normal')
          .text(`Date: ${new Date().toLocaleDateString()}`)
          .text(`Time: ${new Date().toLocaleTimeString()}`)
          .text(`Receipt #: ${receiptData.receiptNumber}`)
          .text('--------------------------------');

        // Print items
        receiptData.items.forEach(item => {
          this.printer
            .text(`${item.name}`)
            .text(`  ${item.quantity} x $${item.price.toFixed(2)} = $${(item.quantity * item.price).toFixed(2)}`);

          // Show discount if applicable
          if (item.discountAmount > 0) {
            this.printer.text(`    Discount: -$${item.discountAmount.toFixed(2)}`);
          }
        });

        this.printer
          .text('--------------------------------')
          .text(`Subtotal: $${receiptData.subtotal.toFixed(2)}`)
          .text(`Tax: $${receiptData.tax.toFixed(2)}`)
          .style('bu')
          .text(`Total: $${receiptData.total.toFixed(2)}`)
          .style('normal')
          .text(`Payment: ${receiptData.paymentMethod}`)
          .text(`Change: $${receiptData.change.toFixed(2)}`)
          .text('--------------------------------')
          .align('ct')
          .text('Thank you for your business!')
          .text('Please come again')
          .feed(3)
          .cut()
          .close(() => {
            console.log('Receipt printed successfully');
            resolve();
          });
      } catch (error) {
        reject(error);
      }
    });
  }

  async printBarcode(data, type = 'CODE128') {
    if (!this.printer) {
      throw new Error('Printer not connected');
    }

    return new Promise((resolve, reject) => {
      try {
        this.printer
          .align('ct')
          .barcode(data, type, {
            width: 2,
            height: 100,
            includetext: true,
            position: 'BLW'
          })
          .feed(2)
          .cut()
          .close(() => {
            resolve();
          });
      } catch (error) {
        reject(error);
      }
    });
  }

  async testPrint() {
    if (!this.printer) {
      throw new Error('Printer not connected');
    }

    return new Promise((resolve, reject) => {
      this.printer
        .font('a')
        .align('ct')
        .style('bu')
        .size(1, 1)
        .text('PRINTER TEST')
        .text('--------------------------------')
        .align('lt')
        .style('normal')
        .text('This is a test print')
        .text(`Date: ${new Date().toLocaleString()}`)
        .text('If you can read this,')
        .text('the printer is working correctly.')
        .feed(3)
        .cut()
        .close(() => {
          resolve();
        });
    });
  }

  disconnect() {
    if (this.device) {
      this.device.close();
      this.device = null;
      this.printer = null;
    }
  }
}
```

### Receipt Format Configuration
```javascript
// receipt-formatter.js
class ReceiptFormatter {
  constructor(config = {}) {
    this.config = {
      storeName: config.storeName || 'POS SYSTEM',
      storeAddress: config.storeAddress || '',
      storePhone: config.storePhone || '',
      taxRate: config.taxRate || 0.08,
      currency: config.currency || '$',
      ...config
    };
  }

  formatReceipt(transaction) {
    const subtotal = this.calculateSubtotal(transaction.items);
    const totalDiscount = this.calculateTotalDiscount(transaction.items);
    const taxableAmount = subtotal - totalDiscount;
    const tax = taxableAmount * this.config.taxRate;
    const total = taxableAmount + tax;

    return {
      storeName: this.config.storeName,
      storeAddress: this.config.storeAddress,
      storePhone: this.config.storePhone,
      receiptNumber: this.generateReceiptNumber(),
      date: new Date().toLocaleDateString(),
      time: new Date().toLocaleTimeString(),
      items: transaction.items.map(item => ({
        name: item.name,
        quantity: item.quantity,
        price: item.price,
        total: item.quantity * item.price,
        discountAmount: item.discountAmount || 0,
        finalTotal: (item.quantity * item.price) - (item.discountAmount || 0)
      })),
      subtotal: subtotal,
      totalDiscount: totalDiscount,
      tax: tax,
      total: total,
      paymentMethod: transaction.paymentMethod,
      amountPaid: transaction.amountPaid,
      change: transaction.amountPaid - total,
      cashier: transaction.cashier || 'System'
    };
  }

  calculateSubtotal(items) {
    return items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
  }

  calculateTotalDiscount(items) {
    return items.reduce((sum, item) => sum + (item.discountAmount || 0), 0);
  }

  generateReceiptNumber() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `R${timestamp}${random}`.slice(-10);
  }
}
```

## Barcode Discount System

### Database Schema
```sql
-- SQLite database schema for discount system
CREATE TABLE products (
    id INTEGER PRIMARY KEY,
    barcode VARCHAR(50) UNIQUE,
    name VARCHAR(255),
    price DECIMAL(10,2),
    category_id INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE discounts (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255),
    type VARCHAR(20), -- 'percentage', 'fixed_amount', 'buy_x_get_y'
    value DECIMAL(10,2),
    min_quantity INTEGER DEFAULT 1,
    max_uses INTEGER,
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE product_discounts (
    id INTEGER PRIMARY KEY,
    product_id INTEGER,
    discount_id INTEGER,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (discount_id) REFERENCES discounts(id)
);

CREATE TABLE discount_barcodes (
    id INTEGER PRIMARY KEY,
    barcode VARCHAR(50) UNIQUE,
    discount_id INTEGER,
    FOREIGN KEY (discount_id) REFERENCES discounts(id)
);

CREATE TABLE transactions (
    id INTEGER PRIMARY KEY,
    receipt_number VARCHAR(50),
    total_amount DECIMAL(10,2),
    discount_amount DECIMAL(10,2),
    tax_amount DECIMAL(10,2),
    payment_method VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Discount Manager Implementation
```javascript
// discount-manager.js
const sqlite3 = require('sqlite3').verbose();

class DiscountManager {
  constructor(dbPath) {
    this.db = new sqlite3.Database(dbPath);
    this.initializeDatabase();
  }

  initializeDatabase() {
    // Create tables if they don't exist
    const tables = [
      `CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY,
        barcode VARCHAR(50) UNIQUE,
        name VARCHAR(255),
        price DECIMAL(10,2),
        category_id INTEGER,
        is_active BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS discounts (
        id INTEGER PRIMARY KEY,
        name VARCHAR(255),
        type VARCHAR(20),
        value DECIMAL(10,2),
        min_quantity INTEGER DEFAULT 1,
        max_uses INTEGER,
        start_date DATE,
        end_date DATE,
        is_active BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS product_discounts (
        id INTEGER PRIMARY KEY,
        product_id INTEGER,
        discount_id INTEGER,
        FOREIGN KEY (product_id) REFERENCES products(id),
        FOREIGN KEY (discount_id) REFERENCES discounts(id)
      )`,
      `CREATE TABLE IF NOT EXISTS discount_barcodes (
        id INTEGER PRIMARY KEY,
        barcode VARCHAR(50) UNIQUE,
        discount_id INTEGER,
        FOREIGN KEY (discount_id) REFERENCES discounts(id)
      )`
    ];

    tables.forEach(sql => {
      this.db.run(sql);
    });
  }

  async identifyBarcode(barcode) {
    return new Promise((resolve, reject) => {
      // Check if it's a product
      this.db.get(
        'SELECT * FROM products WHERE barcode = ? AND is_active = 1',
        [barcode],
        (err, product) => {
          if (err) {
            reject(err);
            return;
          }

          if (product) {
            resolve({ type: 'product', data: product });
            return;
          }

          // Check if it's a discount barcode
          this.db.get(`
            SELECT db.*, d.* FROM discount_barcodes db
            JOIN discounts d ON db.discount_id = d.id
            WHERE db.barcode = ? AND d.is_active = 1
            AND (d.start_date IS NULL OR d.start_date <= date('now'))
            AND (d.end_date IS NULL OR d.end_date >= date('now'))
          `, [barcode], (err, discount) => {
            if (err) {
              reject(err);
              return;
            }

            if (discount) {
              resolve({ type: 'discount', data: discount });
            } else {
              resolve({ type: 'unknown', data: null });
            }
          });
        }
      );
    });
  }

  async getProductDiscounts(productId) {
    return new Promise((resolve, reject) => {
      this.db.all(`
        SELECT d.* FROM discounts d
        JOIN product_discounts pd ON d.id = pd.discount_id
        WHERE pd.product_id = ? AND d.is_active = 1
        AND (d.start_date IS NULL OR d.start_date <= date('now'))
        AND (d.end_date IS NULL OR d.end_date >= date('now'))
      `, [productId], (err, discounts) => {
        if (err) {
          reject(err);
        } else {
          resolve(discounts);
        }
      });
    });
  }

  calculateDiscount(discount, originalPrice, quantity = 1) {
    let discountAmount = 0;

    switch (discount.type) {
      case 'percentage':
        discountAmount = (originalPrice * quantity) * (discount.value / 100);
        break;

      case 'fixed_amount':
        discountAmount = Math.min(discount.value, originalPrice * quantity);
        break;

      case 'buy_x_get_y':
        const freeItems = Math.floor(quantity / discount.min_quantity);
        discountAmount = freeItems * originalPrice;
        break;

      default:
        discountAmount = 0;
    }

    return Math.round(discountAmount * 100) / 100;
  }

  applyDiscount(cartItem, discount) {
    const discountAmount = this.calculateDiscount(
      discount,
      cartItem.price,
      cartItem.quantity
    );

    return {
      ...cartItem,
      originalPrice: cartItem.price,
      discountAmount: discountAmount,
      finalPrice: cartItem.price - (discountAmount / cartItem.quantity),
      appliedDiscount: discount
    };
  }
}
```

### Shopping Cart with Discount Integration
```javascript
// shopping-cart.js
class ShoppingCart {
  constructor(discountManager) {
    this.items = [];
    this.discountManager = discountManager;
    this.cartDiscounts = [];
  }

  async addItem(barcode, quantity = 1) {
    try {
      const barcodeInfo = await this.discountManager.identifyBarcode(barcode);

      if (barcodeInfo.type === 'product') {
        await this.addProduct(barcodeInfo.data, quantity);
      } else if (barcodeInfo.type === 'discount') {
        await this.addDiscount(barcodeInfo.data);
      } else {
        throw new Error('Unknown barcode');
      }
    } catch (error) {
      console.error('Error adding item:', error);
      throw error;
    }
  }

  async addProduct(product, quantity) {
    const existingItem = this.items.find(item => item.productId === product.id);

    if (existingItem) {
      existingItem.quantity += quantity;
    } else {
      this.items.push({
        productId: product.id,
        barcode: product.barcode,
        name: product.name,
        price: product.price,
        quantity: quantity,
        originalPrice: product.price,
        discountAmount: 0,
        finalPrice: product.price
      });
    }

    await this.applyProductDiscounts(product.id);
    this.recalculateCart();
  }

  async addDiscount(discount) {
    this.cartDiscounts.push(discount);
    this.recalculateCart();
  }

  async applyProductDiscounts(productId) {
    try {
      const discounts = await this.discountManager.getProductDiscounts(productId);
      const cartItem = this.items.find(item => item.productId === productId);

      if (cartItem && discounts.length > 0) {
        let bestDiscount = null;
        let maxDiscountAmount = 0;

        discounts.forEach(discount => {
          const discountAmount = this.discountManager.calculateDiscount(
            discount,
            cartItem.price,
            cartItem.quantity
          );

          if (discountAmount > maxDiscountAmount) {
            maxDiscountAmount = discountAmount;
            bestDiscount = discount;
          }
        });

        if (bestDiscount) {
          const updatedItem = this.discountManager.applyDiscount(cartItem, bestDiscount);
          Object.assign(cartItem, updatedItem);
        }
      }
    } catch (error) {
      console.error('Error applying product discounts:', error);
    }
  }

  recalculateCart() {
    this.cartDiscounts.forEach(discount => {
      if (discount.type === 'percentage') {
        const cartSubtotal = this.getSubtotal();
        const discountAmount = cartSubtotal * (discount.value / 100);
        this.distributeCartDiscount(discountAmount);
      }
    });
  }

  distributeCartDiscount(totalDiscountAmount) {
    const subtotal = this.getSubtotal();

    this.items.forEach(item => {
      const itemTotal = item.price * item.quantity;
      const itemDiscountRatio = itemTotal / subtotal;
      const itemDiscountAmount = totalDiscountAmount * itemDiscountRatio;

      item.discountAmount += itemDiscountAmount;
      item.finalPrice = item.price - (item.discountAmount / item.quantity);
    });
  }

  getSubtotal() {
    return this.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  }

  getTotalDiscount() {
    return this.items.reduce((total, item) => {
      return total + item.discountAmount;
    }, 0);
  }

  getTotal() {
    return this.getSubtotal() - this.getTotalDiscount();
  }

  removeItem(productId) {
    this.items = this.items.filter(item => item.productId !== productId);
    this.recalculateCart();
  }

  clear() {
    this.items = [];
    this.cartDiscounts = [];
  }
}
```

## Installation & Setup

### Prerequisites
```bash
# Node.js 16+ required
node --version

# Install Electron globally
npm install -g electron

# Create new Electron project
mkdir electron-pos-system
cd electron-pos-system
npm init -y
```

### Package Installation
```bash
# Core Electron dependencies
npm install --save electron

# Hardware integration packages
npm install --save \
  node-hid \
  serialport \
  usb-detection \
  escpos \
  escpos-usb \
  escpos-serialport \
  node-webcam \
  quagga \
  sqlite3

# Development dependencies
npm install --save-dev \
  electron-builder \
  nodemon
```

### Project Structure
```
electron-pos-system/
├── main.js                 # Electron main process
├── renderer.js             # Renderer process
├── index.html             # Main UI
├── package.json           # Dependencies
├── hardware/              # Hardware integration modules
│   ├── barcode-scanner.js
│   ├── cash-drawer.js
│   ├── thermal-printer.js
│   ├── camera-manager.js
│   └── discount-manager.js
├── database/              # Database files
│   └── pos.db
├── assets/                # Images, sounds, etc.
└── dist/                  # Built application
```

### Main Process Setup (main.js)
```javascript
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// Hardware modules
const BarcodeScanner = require('./hardware/barcode-scanner');
const CashDrawer = require('./hardware/cash-drawer');
const ThermalPrinter = require('./hardware/thermal-printer');
const DiscountManager = require('./hardware/discount-manager');

let mainWindow;
let scanner;
let cashDrawer;
let printer;
let discountManager;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  mainWindow.loadFile('index.html');

  // Initialize hardware
  initializeHardware();
}

async function initializeHardware() {
  try {
    // Initialize discount manager
    discountManager = new DiscountManager('./database/pos.db');

    // Initialize barcode scanner
    scanner = new BarcodeScanner();
    await scanner.connect(0x05e0, 0x1200); // Adjust vendor/product IDs

    scanner.onData((barcode) => {
      mainWindow.webContents.send('barcode-scanned', barcode);
    });

    // Initialize cash drawer
    cashDrawer = new CashDrawer('COM1'); // Adjust port

    // Initialize thermal printer
    printer = new ThermalPrinter('usb', {
      vendorId: 0x04b8,
      productId: 0x0202
    });
    await printer.connect();

    console.log('Hardware initialized successfully');
  } catch (error) {
    console.error('Hardware initialization failed:', error);
  }
}

// IPC handlers
ipcMain.handle('open-cash-drawer', async () => {
  try {
    await cashDrawer.openDrawer();
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('print-receipt', async (event, receiptData) => {
  try {
    await printer.printReceipt(receiptData);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('identify-barcode', async (event, barcode) => {
  try {
    const result = await discountManager.identifyBarcode(barcode);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  // Cleanup hardware connections
  if (scanner) scanner.disconnect();
  if (cashDrawer) cashDrawer.close();
  if (printer) printer.disconnect();

  if (process.platform !== 'darwin') {
    app.quit();
  }
});
```

## Configuration

### Hardware Configuration File (config.json)
```json
{
  "hardware": {
    "barcodeScanner": {
      "type": "usb",
      "vendorId": "0x05e0",
      "productId": "0x1200",
      "enabled": true
    },
    "cashDrawer": {
      "type": "serial",
      "port": "COM1",
      "baudRate": 9600,
      "enabled": true
    },
    "printer": {
      "type": "usb",
      "vendorId": "0x04b8",
      "productId": "0x0202",
      "enabled": true
    },
    "camera": {
      "enabled": true,
      "resolution": {
        "width": 1280,
        "height": 720
      }
    }
  },
  "store": {
    "name": "My Store",
    "address": "123 Main St, City, State 12345",
    "phone": "(*************",
    "taxRate": 0.08
  },
  "receipt": {
    "printLogo": true,
    "logoPath": "./assets/logo.png",
    "footerMessage": "Thank you for your business!"
  }
}
```

### Environment Setup
```bash
# Windows - Install USB drivers
# Download and install vendor-specific drivers for your hardware

# Linux - Add user to dialout group for serial access
sudo usermod -a -G dialout $USER

# macOS - Install Homebrew and dependencies
brew install node
```

## Troubleshooting

### Common Issues

#### 1. Scanner Not Detected
```javascript
// Debug scanner connection
const HID = require('node-hid');

function debugScanner() {
  console.log('Available HID devices:');
  const devices = HID.devices();
  devices.forEach(device => {
    console.log(`VID: 0x${device.vendorId.toString(16)}, PID: 0x${device.productId.toString(16)}, Product: ${device.product}`);
  });
}

debugScanner();
```

#### 2. Serial Port Issues
```javascript
// List available serial ports
const { SerialPort } = require('serialport');

async function listPorts() {
  try {
    const ports = await SerialPort.list();
    console.log('Available serial ports:');
    ports.forEach(port => {
      console.log(`${port.path} - ${port.manufacturer}`);
    });
  } catch (error) {
    console.error('Error listing ports:', error);
  }
}

listPorts();
```

#### 3. Printer Connection Problems
```javascript
// Test printer connection
async function testPrinter() {
  const printer = new ThermalPrinter('usb');

  try {
    await printer.connect();
    await printer.testPrint();
    console.log('Printer test successful');
  } catch (error) {
    console.error('Printer test failed:', error);
  } finally {
    printer.disconnect();
  }
}
```

#### 4. Permission Issues (Linux/macOS)
```bash
# Add udev rules for USB devices (Linux)
sudo nano /etc/udev/rules.d/99-pos-hardware.rules

# Add lines like:
SUBSYSTEM=="usb", ATTR{idVendor}=="04b8", ATTR{idProduct}=="0202", MODE="0666"

# Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger
```

### Hardware Compatibility

#### Supported Barcode Scanners
- **USB HID**: Most USB barcode scanners work as keyboard input
- **Serial**: RS232 scanners with configurable baud rates
- **Bluetooth**: Requires additional pairing setup

#### Supported Printers
- **Epson TM Series**: TM-T88V, TM-T88VI, TM-T20, TM-T82
- **Star Micronics**: TSP100, TSP650, TSP700
- **Citizen**: CT-S310, CT-S4000
- **Generic ESC/POS**: Most thermal printers supporting ESC/POS

#### Supported Cash Drawers
- **Serial**: Standard RJ11/RJ12 connection
- **USB**: USB-powered drawers
- **Printer-driven**: Connected through printer port

### Performance Optimization

#### 1. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_discounts_active ON discounts(is_active, start_date, end_date);
CREATE INDEX idx_product_discounts_product ON product_discounts(product_id);
```

#### 2. Memory Management
```javascript
// Implement connection pooling for hardware
class HardwareManager {
  constructor() {
    this.connections = new Map();
    this.connectionPool = {
      maxConnections: 5,
      timeout: 30000
    };
  }

  async getConnection(deviceType, config) {
    const key = `${deviceType}_${JSON.stringify(config)}`;

    if (this.connections.has(key)) {
      return this.connections.get(key);
    }

    // Create new connection
    const connection = await this.createConnection(deviceType, config);
    this.connections.set(key, connection);

    return connection;
  }

  cleanup() {
    this.connections.forEach(connection => {
      if (connection.disconnect) {
        connection.disconnect();
      }
    });
    this.connections.clear();
  }
}
```

## Conclusion

This guide provides a complete migration path from your existing C# POS system to Electron.js while maintaining all hardware functionality. The modular architecture allows for easy maintenance and future hardware additions.

### Key Benefits of Electron.js Migration:
- **Cross-platform compatibility**: Windows, macOS, Linux
- **Modern web technologies**: HTML5, CSS3, JavaScript
- **Rich ecosystem**: NPM packages for hardware integration
- **Easier maintenance**: Web-based UI development
- **Future-proof**: Regular updates and community support

### Next Steps:
1. Set up development environment
2. Test hardware connections with provided code
3. Implement core POS functionality
4. Migrate existing database structure
5. Test thoroughly with actual hardware
6. Deploy and train users

For additional support or specific hardware integration questions, refer to the individual hardware manufacturer documentation and the respective NPM package documentation.
```
```
```
