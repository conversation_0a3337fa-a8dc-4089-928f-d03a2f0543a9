# Integrated Management System Analysis Report

## Executive Summary

This report provides a comprehensive analysis of the existing Point of Sale (POS) system infrastructure and outlines the requirements for integrating three separate systems into a unified application. The current system consists of three independent modules: Point of Sale (POS), Store Manager (Inventory), and Theater Management (POSTheater), each operating as separate executable files.

## 1. Overview of Existing System

### 1.1 System Architecture
The current system consists of three main components:

1. **Point of Sale (POS) System** - `POS.exe`
2. **Store Manager (Inventory Management)** - `StoreManager.exe`  
3. **Theater Management System** - `POSTheater.exe`

Each system operates independently with its own executable file, requiring separate launches and potentially separate user authentication.

### 1.2 Current Limitations
- **Fragmented User Experience**: Users must switch between three different applications
- **Data Silos**: Limited integration between systems may lead to data inconsistencies
- **Maintenance Overhead**: Three separate applications require individual updates and maintenance
- **Training Complexity**: Staff must learn three different interfaces

## 2. Technical Analysis

### 2.1 Framework and Technology Stack

**Primary Framework**: Microsoft .NET Framework 4.5
- All three applications are built using .NET Framework 4.5
- Applications are compiled as Windows executable files (.exe)
- Configuration files indicate standard .NET application structure

**Development Environment**: 
- Microsoft Visual Studio (Version ******** or compatible)
- Solution file indicates Visual Studio project structure
- C# appears to be the primary programming language based on project structure

### 2.2 Database Infrastructure

**Database System**: Microsoft SQL Server
- Database name: `POS`
- SQL Server Express appears to be the target deployment
- Database scripts indicate SQL Server 2012 compatibility level
- Connection strings are configured but not visible in current config files

**Database Features**:
- Full-text search capabilities enabled
- Standard SQL Server database with .mdf and .ldf files
- Located in standard SQL Server Express directory structure

### 2.3 Hardware Integration Capabilities

The system includes extensive hardware integration support:

**POS Hardware Support**:
- **OPOS (OLE for Retail POS)** integration
- **Microsoft POS for .NET** framework
- Line display support (USB and OPOS)
- Receipt printer integration
- Cash drawer control
- Barcode scanner support
- Payment terminal integration

**Hardware Components Identified**:
- Line displays (USB and OPOS variants)
- Receipt printers
- Cash registers
- Barcode scanners
- Payment processing devices
- Document scanners (TWAIN support via DynamicDotNetTWAIN.dll)

### 2.4 Reporting Infrastructure

**Store Manager Reporting Capabilities**:
- Daily sales reports
- Shift sales reports
- Purchase history reports
- Inventory reorder level reports
- Best selling products analysis
- Profit analysis reports
- Hourly sales tracking
- Inventory summary reports

**Report Format**: Microsoft Report Definition Language (.rdlc files)

## 3. Current File Structure Analysis

### 3.1 Main Application Directories

```
├── POS/                          # Point of Sale System
│   ├── POS.exe                   # Main POS executable
│   ├── POS.exe.config           # Configuration file
│   ├── POS.xml                  # Assembly metadata
│   ├── AWSSDK.Core.dll          # AWS SDK integration
│   ├── AWSSDK.SimpleEmail.dll   # Email functionality
│   ├── Microsoft.PointOfService.dll # POS hardware support
│   └── DynamicDotNetTWAIN.dll   # Scanner integration
│
├── StoreManager/                 # Inventory Management System
│   ├── StoreManager.exe         # Main inventory executable
│   ├── StoreManager.exe.config  # Configuration file
│   ├── StoreManager.xml         # Assembly metadata
│   ├── Report*.rdlc             # Various report templates
│   └── Microsoft.VisualBasic.PowerPacks.dll # UI components
│
├── POSTheater/                   # Theater Management System
│   ├── POSTheater.exe           # Main theater executable
│   ├── POSTheater.exe.config    # Configuration file
│   ├── POSTheater.xml           # Assembly metadata
│   └── DynamicDotNetTWAIN.dll   # Scanner integration
│
└── SupportApps/                  # Hardware drivers and utilities
    ├── OPOS_ADK_DOTNET_v1146/   # OPOS development kit
    ├── Line-Display_USB_Win8/    # Line display drivers
    ├── POSforDotNet-1.14.msi    # POS.NET framework installer
    └── ReportViewer.msi         # Report viewing components
```

### 3.2 Database Structure
- Main database: `POS`
- Backup files: `DB.bak`, `20180913.bak`
- SQL scripts: `script.sql`, `script12262022.sql`

## 4. Feature Analysis by Module

### 4.1 Point of Sale (POS) System Features
Based on resource analysis and file structure:

- **Transaction Processing**: Cash, credit, and debit payment handling
- **Product Management**: Barcode scanning and product lookup
- **Receipt Printing**: Thermal printer integration
- **Cash Drawer Control**: Automatic cash drawer opening
- **Customer Display**: Line display for customer-facing information
- **Email Integration**: AWS SES for receipt delivery
- **User Interface**: Touch-friendly interface with numeric keypad

### 4.2 Store Manager (Inventory) Features
Based on report templates and configuration:

- **Inventory Management**: Stock level tracking and management
- **Purchase Management**: Supplier and purchase order handling
- **Sales Analytics**: Comprehensive sales reporting
- **Reorder Management**: Automatic reorder level alerts
- **Profit Analysis**: Best profit and selling product analysis
- **Time-based Reports**: Daily, hourly, and shift-based reporting
- **Data Export**: Report generation and export capabilities

### 4.3 Theater Management Features
Based on file structure (limited information available):

- **Theater-specific Operations**: Specialized for entertainment venue management
- **Document Scanning**: TWAIN scanner integration
- **Basic POS Functionality**: Core transaction processing

## 5. Reverse Engineering Analysis

### 5.1 Feasibility Assessment

**High Feasibility** for .NET applications:
- .NET assemblies contain rich metadata
- Decompilation tools can recover near-source-code quality
- No apparent obfuscation detected in current files

### 5.2 Recommended Tools

**Primary Decompilation Tools**:
1. **JetBrains dotPeek** (Free)
   - Export to Visual Studio projects
   - Excellent C# code reconstruction
   - Assembly browsing capabilities

2. **ILSpy** (Open Source)
   - Cross-platform decompilation
   - Plugin ecosystem
   - Command-line interface available

3. **Reflexil** (Free)
   - Assembly editing capabilities
   - Integration with other tools
   - Useful for understanding structure

### 5.3 Reverse Engineering Process

1. **Assembly Analysis**: Extract metadata and understand class structures
2. **Code Decompilation**: Convert IL code back to C# source
3. **Resource Extraction**: Extract embedded resources, images, and strings
4. **Database Schema Recovery**: Analyze connection strings and data access patterns
5. **Configuration Analysis**: Understand application settings and dependencies

## 6. Integration Strategy

### 6.1 Unified Application Architecture

**Recommended Approach**: Single application with role-based modules
- **Single Executable**: One main application entry point
- **Modular Design**: Separate modules for POS, Inventory, and Theater
- **Shared Components**: Common database access, user management, and reporting
- **Role-Based Access Control (RBAC)**: Module access based on user roles

### 6.2 User Roles and Access Control

**Proposed Role Structure**:
1. **Administrator**: Full system access
2. **Manager**: POS + Inventory access
3. **Cashier**: POS-only access
4. **Inventory Clerk**: Inventory-only access
5. **Theater Operator**: Theater + basic POS access

### 6.3 Database Integration Strategy

**Approach**: Extend existing database schema
- **Preserve Current Data**: Maintain existing table structures
- **Add User Management**: Implement role-based user tables
- **Audit Trail**: Add logging and audit capabilities
- **Data Consistency**: Implement proper foreign key relationships

## 7. Modern UI/UX Recommendations

### 7.1 Technology Stack for New System

**Recommended Framework**: Windows Presentation Foundation (WPF) or Windows UI 3 (WinUI 3)
- **WPF**: Mature, extensive control library, good hardware integration
- **WinUI 3**: Modern, future-proof, better performance

**UI Design Principles**:
- **Responsive Design**: Adapt to different screen sizes
- **Touch-Friendly**: Large buttons and touch targets
- **Accessibility**: Screen reader support and keyboard navigation
- **Consistent Branding**: Unified visual identity across modules

### 7.2 User Experience Improvements

**Navigation**:
- **Dashboard**: Central hub showing key metrics and quick actions
- **Module Switching**: Easy navigation between POS, Inventory, and Theater
- **Context-Aware Interface**: Show relevant options based on user role

**Performance**:
- **Fast Startup**: Quick application launch
- **Responsive Operations**: Smooth transaction processing
- **Offline Capability**: Basic operations when database is unavailable

## 8. Development Recommendations

### 8.1 Recommended Development Environment

**IDE**: Visual Studio 2022 Community/Professional
- **Reason**: Best .NET development experience, integrated debugging, NuGet package management

**Alternative**: JetBrains Rider
- **Reason**: Cross-platform, excellent refactoring tools, integrated version control

### 8.2 Project Structure

**Solution Organization**:
```
IntegratedPOS.sln
├── IntegratedPOS.Core/           # Shared business logic
├── IntegratedPOS.Data/           # Database access layer
├── IntegratedPOS.POS/            # POS module
├── IntegratedPOS.Inventory/      # Inventory module
├── IntegratedPOS.Theater/        # Theater module
├── IntegratedPOS.Common/         # Shared utilities
├── IntegratedPOS.Hardware/       # Hardware integration
└── IntegratedPOS.Reports/        # Reporting engine
```

### 8.3 Migration Strategy

**Phase 1**: Reverse engineer and understand current systems
**Phase 2**: Design unified database schema and user management
**Phase 3**: Develop core framework and shared components
**Phase 4**: Migrate POS module functionality
**Phase 5**: Migrate Inventory module functionality
**Phase 6**: Migrate Theater module functionality
**Phase 7**: Integration testing and deployment

## 9. Hardware Integration Considerations

### 9.1 Maintaining Hardware Compatibility

**Strategy**: Preserve existing hardware integration
- **OPOS Support**: Maintain OPOS service object compatibility
- **POS.NET Framework**: Continue using Microsoft POS for .NET
- **Driver Compatibility**: Ensure existing drivers work with new system

### 9.2 Hardware Testing Requirements

**Critical Components**:
- Receipt printers
- Cash drawers
- Barcode scanners
- Line displays
- Payment terminals
- Document scanners

## 10. Next Steps and Recommendations

### 10.1 Immediate Actions

1. **Reverse Engineer Applications**: Use dotPeek to decompile all three executables
2. **Database Analysis**: Restore database backup and analyze schema
3. **Hardware Inventory**: Document all connected hardware devices
4. **User Requirements**: Interview current users to understand workflows

### 10.2 Development Priorities

1. **User Authentication System**: Implement role-based login
2. **Database Layer**: Create unified data access layer
3. **Core Framework**: Develop modular application framework
4. **POS Module**: Migrate core transaction functionality first
5. **Testing Framework**: Implement comprehensive testing strategy

### 10.3 Risk Mitigation

**Technical Risks**:
- **Hardware Compatibility**: Test all hardware integrations thoroughly
- **Data Migration**: Ensure no data loss during transition
- **Performance**: Maintain or improve transaction processing speed

**Business Risks**:
- **User Training**: Plan comprehensive training program
- **Downtime**: Minimize business disruption during migration
- **Backup Strategy**: Maintain ability to revert to old system if needed

## Conclusion

The integration of the three existing POS systems into a unified application is technically feasible and highly recommended. The .NET Framework foundation provides excellent reverse engineering capabilities, and the existing hardware integration can be preserved. The new system will provide improved user experience, better data consistency, and reduced maintenance overhead while maintaining all current functionality.

The success of this project depends on thorough reverse engineering of the existing systems, careful preservation of business logic, and comprehensive testing of hardware integrations. With proper planning and execution, this integration will significantly improve operational efficiency and user satisfaction.

---

# Electron.js POS Hardware Integration Guide

## Overview

This comprehensive guide covers migrating your existing C# POS system to Electron.js while maintaining the same hardware functionality and interfaces. The current system uses Microsoft POS for .NET with OPOS drivers, and this guide provides equivalent Electron.js implementations.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Hardware Components](#hardware-components)
3. [USB Camera Integration](#usb-camera-integration)
4. [Barcode Scanner Integration](#barcode-scanner-integration)
5. [Cash Drawer Integration](#cash-drawer-integration)
6. [Receipt Printer Integration](#receipt-printer-integration)
7. [Barcode Discount System](#barcode-discount-system)
8. [Installation & Setup](#installation--setup)
9. [Configuration](#configuration)
10. [Troubleshooting](#troubleshooting)

## System Architecture

### Current C# Architecture
- **Framework**: Microsoft POS for .NET v1.14
- **Hardware Layer**: OPOS (OLE for Retail POS)
- **Communication**: USB HID, Serial, OPOS drivers
- **Libraries**: Microsoft.PointOfService.dll, DynamicDotNetTWAIN.dll

### Electron.js Architecture
```
┌─────────────────────────────────────┐
│           Electron Main Process     │
├─────────────────────────────────────┤
│  Hardware Abstraction Layer (HAL)   │
├─────────────────────────────────────┤
│  USB HID │ Serial │ Camera │ Network │
├─────────────────────────────────────┤
│ Scanner │ Printer │ Drawer │ Camera  │
└─────────────────────────────────────┘
```

## Hardware Components

### Required Node.js Packages
```bash
npm install --save \
  node-hid \
  serialport \
  escpos \
  escpos-usb \
  escpos-serialport \
  node-webcam \
  quagga \
  sqlite3 \
  usb-detection
```

### Hardware Connection Types

| Device Type | Connection | Port/Interface | Driver Required |
|-------------|------------|----------------|-----------------|
| Barcode Scanner | USB HID | USB Port | HID Driver |
| Receipt Printer | USB/Serial | USB/COM Port | ESC/POS |
| Cash Drawer | Serial/USB | COM Port/USB | Serial Driver |
| USB Camera | USB | USB Port | UVC Driver |
| Line Display | USB/Serial | USB/COM Port | USB/Serial |

## USB Camera Integration

### Implementation Options

#### Option 1: Web APIs (Recommended)
```javascript
// camera-manager.js
class CameraManager {
  constructor() {
    this.stream = null;
    this.isActive = false;
  }

  async initialize() {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'environment'
        }
      });

      const videoElement = document.getElementById('camera-preview');
      videoElement.srcObject = this.stream;
      this.isActive = true;
      return true;
    } catch (error) {
      console.error('Camera initialization failed:', error);
      return false;
    }
  }

  captureImage() {
    if (!this.isActive) throw new Error('Camera not initialized');

    const canvas = document.createElement('canvas');
    const video = document.querySelector('video');

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);

    return canvas.toDataURL('image/jpeg', 0.8);
  }

  stop() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.isActive = false;
    }
  }
}
```

#### Option 2: Node.js Library
```javascript
// node-camera.js
const NodeWebcam = require('node-webcam');

class NodeCamera {
  constructor() {
    this.webcam = NodeWebcam.create({
      width: 1280,
      height: 720,
      quality: 100,
      delay: 0,
      saveShots: true,
      output: "jpeg",
      device: false,
      callbackReturn: "location",
      verbose: false
    });
  }

  async capture(filename) {
    return new Promise((resolve, reject) => {
      this.webcam.capture(filename, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
  }
}
```

## Barcode Scanner Integration

### USB HID Scanner
```javascript
// barcode-scanner.js
const HID = require('node-hid');
const usbDetection = require('usb-detection');

class BarcodeScanner {
  constructor() {
    this.device = null;
    this.isConnected = false;
    this.onDataCallback = null;
  }

  async connect(vendorId, productId) {
    try {
      const devices = HID.devices();
      const scanner = devices.find(d =>
        d.vendorId === vendorId && d.productId === productId
      );

      if (scanner) {
        this.device = new HID.HID(scanner.path);
        this.isConnected = true;
        this.setupDataListener();
        console.log('Barcode scanner connected');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Scanner connection failed:', error);
      return false;
    }
  }

  setupDataListener() {
    this.device.on('data', (data) => {
      const barcode = this.parseBarcode(data);
      if (barcode && this.onDataCallback) {
        this.onDataCallback(barcode);
      }
    });

    this.device.on('error', (error) => {
      console.error('Scanner error:', error);
      this.isConnected = false;
    });
  }

  parseBarcode(data) {
    let barcode = '';
    for (let i = 0; i < data.length; i++) {
      if (data[i] > 0 && data[i] < 255) {
        barcode += String.fromCharCode(data[i]);
      }
    }
    return barcode.trim();
  }

  onData(callback) {
    this.onDataCallback = callback;
  }

  disconnect() {
    if (this.device) {
      this.device.close();
      this.isConnected = false;
    }
  }
}
```

### Serial Scanner
```javascript
// serial-scanner.js
const { SerialPort } = require('serialport');
const { ReadlineParser } = require('@serialport/parser-readline');

class SerialBarcodeScanner {
  constructor(portPath, baudRate = 9600) {
    this.port = new SerialPort({
      path: portPath,
      baudRate: baudRate,
      dataBits: 8,
      parity: 'none',
      stopBits: 1
    });

    this.parser = this.port.pipe(new ReadlineParser({ delimiter: '\r\n' }));
    this.setupListeners();
  }

  setupListeners() {
    this.parser.on('data', (data) => {
      const barcode = data.toString().trim();
      if (barcode && this.onDataCallback) {
        this.onDataCallback(barcode);
      }
    });

    this.port.on('error', (error) => {
      console.error('Serial scanner error:', error);
    });
  }

  onData(callback) {
    this.onDataCallback = callback;
  }
}
```

### Camera-based Scanning
```javascript
// camera-scanner.js
const Quagga = require('quagga');

class CameraBarcodeScanner {
  constructor() {
    this.isScanning = false;
  }

  initialize() {
    return new Promise((resolve, reject) => {
      Quagga.init({
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: document.querySelector('#camera-scanner'),
          constraints: {
            width: 640,
            height: 480,
            facingMode: "environment"
          }
        },
        decoder: {
          readers: [
            "code_128_reader",
            "ean_reader",
            "ean_8_reader",
            "code_39_reader",
            "codabar_reader",
            "upc_reader",
            "upc_e_reader"
          ]
        }
      }, (err) => {
        if (err) {
          reject(err);
          return;
        }
        Quagga.start();
        this.isScanning = true;
        resolve();
      });
    });
  }

  onDetected(callback) {
    Quagga.onDetected((data) => {
      const barcode = data.codeResult.code;
      callback(barcode);
    });
  }

  stop() {
    if (this.isScanning) {
      Quagga.stop();
      this.isScanning = false;
    }
  }
}
```

## Cash Drawer Integration

### Serial Communication
```javascript
// cash-drawer.js
const { SerialPort } = require('serialport');

class CashDrawer {
  constructor(portPath, baudRate = 9600) {
    this.port = new SerialPort({
      path: portPath,
      baudRate: baudRate,
      dataBits: 8,
      parity: 'none',
      stopBits: 1
    });

    this.isOpen = false;
    this.setupListeners();
  }

  setupListeners() {
    this.port.on('open', () => {
      console.log('Cash drawer port opened');
      this.isOpen = true;
    });

    this.port.on('error', (error) => {
      console.error('Cash drawer error:', error);
      this.isOpen = false;
    });
  }

  openDrawer() {
    return new Promise((resolve, reject) => {
      if (!this.isOpen) {
        reject(new Error('Cash drawer port not open'));
        return;
      }

      // ESC/POS command: ESC p m t1 t2
      const openCommand = Buffer.from([0x1B, 0x70, 0x00, 0x19, 0x19]);

      this.port.write(openCommand, (error) => {
        if (error) {
          reject(error);
        } else {
          console.log('Cash drawer opened');
          resolve();
        }
      });
    });
  }

  checkDrawerStatus() {
    return new Promise((resolve, reject) => {
      const statusCommand = Buffer.from([0x1B, 0x75, 0x00]);

      this.port.write(statusCommand, (error) => {
        if (error) reject(error);
      });

      this.port.once('data', (data) => {
        const isOpen = (data[0] & 0x01) === 0;
        resolve(isOpen);
      });
    });
  }

  close() {
    if (this.port && this.port.isOpen) {
      this.port.close();
    }
  }
}
```

### USB Cash Drawer
```javascript
// usb-cash-drawer.js
const HID = require('node-hid');

class USBCashDrawer {
  constructor(vendorId, productId) {
    this.vendorId = vendorId;
    this.productId = productId;
    this.device = null;
  }

  connect() {
    try {
      const devices = HID.devices();
      const drawer = devices.find(d =>
        d.vendorId === this.vendorId && d.productId === this.productId
      );

      if (drawer) {
        this.device = new HID.HID(drawer.path);
        console.log('USB cash drawer connected');
        return true;
      }
      return false;
    } catch (error) {
      console.error('USB drawer connection failed:', error);
      return false;
    }
  }

  openDrawer() {
    if (!this.device) {
      throw new Error('Cash drawer not connected');
    }

    const openCommand = [0x00, 0x01, 0x00, 0x00];
    this.device.write(openCommand);
  }

  disconnect() {
    if (this.device) {
      this.device.close();
      this.device = null;
    }
  }
}
```

### Network/Ethernet Cash Drawer
```javascript
const net = require('net');

class NetworkCashDrawer {
  constructor(host, port) {
    this.host = host;
    this.port = port;
    this.client = null;
  }

  connect() {
    return new Promise((resolve, reject) => {
      this.client = new net.Socket();

      this.client.connect(this.port, this.host, () => {
        console.log('Network cash drawer connected');
        resolve();
      });

      this.client.on('error', (error) => {
        reject(error);
      });
    });
  }

  openDrawer() {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        reject(new Error('Not connected to cash drawer'));
        return;
      }

      const openCommand = Buffer.from([0x1B, 0x70, 0x00, 0x19, 0x19]);

      this.client.write(openCommand, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  disconnect() {
    if (this.client) {
      this.client.destroy();
      this.client = null;
    }
  }
}
```

## Receipt Printer Integration

### ESC/POS Thermal Printer
```javascript
// thermal-printer.js
const escpos = require('escpos');
escpos.USB = require('escpos-usb');
escpos.Serial = require('escpos-serialport');

class ThermalPrinter {
  constructor(connectionType = 'usb', options = {}) {
    this.connectionType = connectionType;
    this.options = options;
    this.device = null;
    this.printer = null;
  }

  async connect() {
    try {
      if (this.connectionType === 'usb') {
        this.device = new escpos.USB(
          this.options.vendorId || 0x04b8, // Epson vendor ID
          this.options.productId || 0x0202  // Product ID
        );
      } else if (this.connectionType === 'serial') {
        this.device = new escpos.Serial(
          this.options.port || 'COM1',
          {
            baudRate: this.options.baudRate || 9600,
            dataBits: 8,
            stopBits: 1,
            parity: 'none'
          }
        );
      }

      this.printer = new escpos.Printer(this.device);

      return new Promise((resolve, reject) => {
        this.device.open((error) => {
          if (error) {
            reject(error);
          } else {
            console.log('Thermal printer connected');
            resolve();
          }
        });
      });
    } catch (error) {
      console.error('Printer connection failed:', error);
      throw error;
    }
  }

  async printReceipt(receiptData) {
    if (!this.printer) {
      throw new Error('Printer not connected');
    }

    return new Promise((resolve, reject) => {
      try {
        this.printer
          .font('a')
          .align('ct')
          .style('bu')
          .size(1, 1)
          .text(receiptData.storeName || 'POS SYSTEM')
          .text('--------------------------------')
          .align('lt')
          .style('normal')
          .text(`Date: ${new Date().toLocaleDateString()}`)
          .text(`Time: ${new Date().toLocaleTimeString()}`)
          .text(`Receipt #: ${receiptData.receiptNumber}`)
          .text('--------------------------------');

        // Print items
        receiptData.items.forEach(item => {
          this.printer
            .text(`${item.name}`)
            .text(`  ${item.quantity} x $${item.price.toFixed(2)} = $${(item.quantity * item.price).toFixed(2)}`);

          // Show discount if applicable
          if (item.discountAmount > 0) {
            this.printer.text(`    Discount: -$${item.discountAmount.toFixed(2)}`);
          }
        });

        this.printer
          .text('--------------------------------')
          .text(`Subtotal: $${receiptData.subtotal.toFixed(2)}`)
          .text(`Tax: $${receiptData.tax.toFixed(2)}`)
          .style('bu')
          .text(`Total: $${receiptData.total.toFixed(2)}`)
          .style('normal')
          .text(`Payment: ${receiptData.paymentMethod}`)
          .text(`Change: $${receiptData.change.toFixed(2)}`)
          .text('--------------------------------')
          .align('ct')
          .text('Thank you for your business!')
          .text('Please come again')
          .feed(3)
          .cut()
          .close(() => {
            console.log('Receipt printed successfully');
            resolve();
          });
      } catch (error) {
        reject(error);
      }
    });
  }

  async printBarcode(data, type = 'CODE128') {
    if (!this.printer) {
      throw new Error('Printer not connected');
    }

    return new Promise((resolve, reject) => {
      try {
        this.printer
          .align('ct')
          .barcode(data, type, {
            width: 2,
            height: 100,
            includetext: true,
            position: 'BLW'
          })
          .feed(2)
          .cut()
          .close(() => {
            resolve();
          });
      } catch (error) {
        reject(error);
      }
    });
  }

  async testPrint() {
    if (!this.printer) {
      throw new Error('Printer not connected');
    }

    return new Promise((resolve, reject) => {
      this.printer
        .font('a')
        .align('ct')
        .style('bu')
        .size(1, 1)
        .text('PRINTER TEST')
        .text('--------------------------------')
        .align('lt')
        .style('normal')
        .text('This is a test print')
        .text(`Date: ${new Date().toLocaleString()}`)
        .text('If you can read this,')
        .text('the printer is working correctly.')
        .feed(3)
        .cut()
        .close(() => {
          resolve();
        });
    });
  }

  disconnect() {
    if (this.device) {
      this.device.close();
      this.device = null;
      this.printer = null;
    }
  }
}
```

### Receipt Format Configuration
```javascript
// receipt-formatter.js
class ReceiptFormatter {
  constructor(config = {}) {
    this.config = {
      storeName: config.storeName || 'POS SYSTEM',
      storeAddress: config.storeAddress || '',
      storePhone: config.storePhone || '',
      taxRate: config.taxRate || 0.08,
      currency: config.currency || '$',
      ...config
    };
  }

  formatReceipt(transaction) {
    const subtotal = this.calculateSubtotal(transaction.items);
    const totalDiscount = this.calculateTotalDiscount(transaction.items);
    const taxableAmount = subtotal - totalDiscount;
    const tax = taxableAmount * this.config.taxRate;
    const total = taxableAmount + tax;

    return {
      storeName: this.config.storeName,
      storeAddress: this.config.storeAddress,
      storePhone: this.config.storePhone,
      receiptNumber: this.generateReceiptNumber(),
      date: new Date().toLocaleDateString(),
      time: new Date().toLocaleTimeString(),
      items: transaction.items.map(item => ({
        name: item.name,
        quantity: item.quantity,
        price: item.price,
        total: item.quantity * item.price,
        discountAmount: item.discountAmount || 0,
        finalTotal: (item.quantity * item.price) - (item.discountAmount || 0)
      })),
      subtotal: subtotal,
      totalDiscount: totalDiscount,
      tax: tax,
      total: total,
      paymentMethod: transaction.paymentMethod,
      amountPaid: transaction.amountPaid,
      change: transaction.amountPaid - total,
      cashier: transaction.cashier || 'System'
    };
  }

  calculateSubtotal(items) {
    return items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
  }

  calculateTotalDiscount(items) {
    return items.reduce((sum, item) => sum + (item.discountAmount || 0), 0);
  }

  generateReceiptNumber() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `R${timestamp}${random}`.slice(-10);
  }
}
```

### Integration with Checkout Process
```javascript
// checkout-manager.js
class CheckoutManager {
  constructor(cashDrawer, printer) {
    this.cashDrawer = cashDrawer;
    this.printer = printer;
  }

  async completeTransaction(transaction) {
    try {
      // 1. Print receipt
      await this.printer.printReceipt(transaction);

      // 2. Open cash drawer for cash payments
      if (transaction.paymentMethod === 'cash') {
        await this.cashDrawer.openDrawer();

        // 3. Wait for drawer to be closed (optional)
        await this.waitForDrawerClose();
      }

      console.log('Transaction completed successfully');
    } catch (error) {
      console.error('Transaction completion failed:', error);
    }
  }

  async waitForDrawerClose() {
    // Poll drawer status until closed
    const checkInterval = setInterval(async () => {
      try {
        const isOpen = await this.cashDrawer.checkDrawerStatus();
        if (!isOpen) {
          clearInterval(checkInterval);
          console.log('Cash drawer closed');
        }
      } catch (error) {
        clearInterval(checkInterval);
        console.error('Error checking drawer status:', error);
      }
    }, 1000);
  }
}
```

## Barcode Discount System

### Database Schema
```sql
-- SQLite database schema for discount system
CREATE TABLE products (
    id INTEGER PRIMARY KEY,
    barcode VARCHAR(50) UNIQUE,
    name VARCHAR(255),
    price DECIMAL(10,2),
    category_id INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE discounts (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255),
    type VARCHAR(20), -- 'percentage', 'fixed_amount', 'buy_x_get_y'
    value DECIMAL(10,2),
    min_quantity INTEGER DEFAULT 1,
    max_uses INTEGER,
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE product_discounts (
    id INTEGER PRIMARY KEY,
    product_id INTEGER,
    discount_id INTEGER,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (discount_id) REFERENCES discounts(id)
);

CREATE TABLE discount_barcodes (
    id INTEGER PRIMARY KEY,
    barcode VARCHAR(50) UNIQUE,
    discount_id INTEGER,
    FOREIGN KEY (discount_id) REFERENCES discounts(id)
);

CREATE TABLE transactions (
    id INTEGER PRIMARY KEY,
    receipt_number VARCHAR(50),
    total_amount DECIMAL(10,2),
    discount_amount DECIMAL(10,2),
    tax_amount DECIMAL(10,2),
    payment_method VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Discount Manager Implementation
```javascript
// discount-manager.js
const sqlite3 = require('sqlite3').verbose();

class DiscountManager {
  constructor(dbPath) {
    this.db = new sqlite3.Database(dbPath);
    this.initializeDatabase();
  }

  initializeDatabase() {
    // Create tables if they don't exist
    const tables = [
      `CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY,
        barcode VARCHAR(50) UNIQUE,
        name VARCHAR(255),
        price DECIMAL(10,2),
        category_id INTEGER,
        is_active BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS discounts (
        id INTEGER PRIMARY KEY,
        name VARCHAR(255),
        type VARCHAR(20),
        value DECIMAL(10,2),
        min_quantity INTEGER DEFAULT 1,
        max_uses INTEGER,
        start_date DATE,
        end_date DATE,
        is_active BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS product_discounts (
        id INTEGER PRIMARY KEY,
        product_id INTEGER,
        discount_id INTEGER,
        FOREIGN KEY (product_id) REFERENCES products(id),
        FOREIGN KEY (discount_id) REFERENCES discounts(id)
      )`,
      `CREATE TABLE IF NOT EXISTS discount_barcodes (
        id INTEGER PRIMARY KEY,
        barcode VARCHAR(50) UNIQUE,
        discount_id INTEGER,
        FOREIGN KEY (discount_id) REFERENCES discounts(id)
      )`
    ];

    tables.forEach(sql => {
      this.db.run(sql);
    });
  }

  async identifyBarcode(barcode) {
    return new Promise((resolve, reject) => {
      // Check if it's a product
      this.db.get(
        'SELECT * FROM products WHERE barcode = ? AND is_active = 1',
        [barcode],
        (err, product) => {
          if (err) {
            reject(err);
            return;
          }

          if (product) {
            resolve({ type: 'product', data: product });
            return;
          }

          // Check if it's a discount barcode
          this.db.get(`
            SELECT db.*, d.* FROM discount_barcodes db
            JOIN discounts d ON db.discount_id = d.id
            WHERE db.barcode = ? AND d.is_active = 1
            AND (d.start_date IS NULL OR d.start_date <= date('now'))
            AND (d.end_date IS NULL OR d.end_date >= date('now'))
          `, [barcode], (err, discount) => {
            if (err) {
              reject(err);
              return;
            }

            if (discount) {
              resolve({ type: 'discount', data: discount });
            } else {
              resolve({ type: 'unknown', data: null });
            }
          });
        }
      );
    });
  }

  async getProductDiscounts(productId) {
    return new Promise((resolve, reject) => {
      this.db.all(`
        SELECT d.* FROM discounts d
        JOIN product_discounts pd ON d.id = pd.discount_id
        WHERE pd.product_id = ? AND d.is_active = 1
        AND (d.start_date IS NULL OR d.start_date <= date('now'))
        AND (d.end_date IS NULL OR d.end_date >= date('now'))
      `, [productId], (err, discounts) => {
        if (err) {
          reject(err);
        } else {
          resolve(discounts);
        }
      });
    });
  }

  calculateDiscount(discount, originalPrice, quantity = 1) {
    let discountAmount = 0;

    switch (discount.type) {
      case 'percentage':
        discountAmount = (originalPrice * quantity) * (discount.value / 100);
        break;

      case 'fixed_amount':
        discountAmount = Math.min(discount.value, originalPrice * quantity);
        break;

      case 'buy_x_get_y':
        const freeItems = Math.floor(quantity / discount.min_quantity);
        discountAmount = freeItems * originalPrice;
        break;

      default:
        discountAmount = 0;
    }

    return Math.round(discountAmount * 100) / 100;
  }

  applyDiscount(cartItem, discount) {
    const discountAmount = this.calculateDiscount(
      discount,
      cartItem.price,
      cartItem.quantity
    );

    return {
      ...cartItem,
      originalPrice: cartItem.price,
      discountAmount: discountAmount,
      finalPrice: cartItem.price - (discountAmount / cartItem.quantity),
      appliedDiscount: discount
    };
  }
}
```

### Shopping Cart with Discount Integration
```javascript
// shopping-cart.js
class ShoppingCart {
  constructor(discountManager) {
    this.items = [];
    this.discountManager = discountManager;
    this.cartDiscounts = [];
  }

  async addItem(barcode, quantity = 1) {
    try {
      const barcodeInfo = await this.discountManager.identifyBarcode(barcode);

      if (barcodeInfo.type === 'product') {
        await this.addProduct(barcodeInfo.data, quantity);
      } else if (barcodeInfo.type === 'discount') {
        await this.addDiscount(barcodeInfo.data);
      } else {
        throw new Error('Unknown barcode');
      }
    } catch (error) {
      console.error('Error adding item:', error);
      throw error;
    }
  }

  async addProduct(product, quantity) {
    const existingItem = this.items.find(item => item.productId === product.id);

    if (existingItem) {
      existingItem.quantity += quantity;
    } else {
      this.items.push({
        productId: product.id,
        barcode: product.barcode,
        name: product.name,
        price: product.price,
        quantity: quantity,
        originalPrice: product.price,
        discountAmount: 0,
        finalPrice: product.price
      });
    }

    await this.applyProductDiscounts(product.id);
    this.recalculateCart();
  }

  async addDiscount(discount) {
    this.cartDiscounts.push(discount);
    this.recalculateCart();
  }

  async applyProductDiscounts(productId) {
    try {
      const discounts = await this.discountManager.getProductDiscounts(productId);
      const cartItem = this.items.find(item => item.productId === productId);

      if (cartItem && discounts.length > 0) {
        let bestDiscount = null;
        let maxDiscountAmount = 0;

        discounts.forEach(discount => {
          const discountAmount = this.discountManager.calculateDiscount(
            discount,
            cartItem.price,
            cartItem.quantity
          );

          if (discountAmount > maxDiscountAmount) {
            maxDiscountAmount = discountAmount;
            bestDiscount = discount;
          }
        });

        if (bestDiscount) {
          const updatedItem = this.discountManager.applyDiscount(cartItem, bestDiscount);
          Object.assign(cartItem, updatedItem);
        }
      }
    } catch (error) {
      console.error('Error applying product discounts:', error);
    }
  }

  recalculateCart() {
    this.cartDiscounts.forEach(discount => {
      if (discount.type === 'percentage') {
        const cartSubtotal = this.getSubtotal();
        const discountAmount = cartSubtotal * (discount.value / 100);
        this.distributeCartDiscount(discountAmount);
      }
    });
  }

  distributeCartDiscount(totalDiscountAmount) {
    const subtotal = this.getSubtotal();

    this.items.forEach(item => {
      const itemTotal = item.price * item.quantity;
      const itemDiscountRatio = itemTotal / subtotal;
      const itemDiscountAmount = totalDiscountAmount * itemDiscountRatio;

      item.discountAmount += itemDiscountAmount;
      item.finalPrice = item.price - (item.discountAmount / item.quantity);
    });
  }

  getSubtotal() {
    return this.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  }

  getTotalDiscount() {
    return this.items.reduce((total, item) => {
      return total + item.discountAmount;
    }, 0);
  }

  getTotal() {
    return this.getSubtotal() - this.getTotalDiscount();
  }

  removeItem(productId) {
    this.items = this.items.filter(item => item.productId !== productId);
    this.recalculateCart();
  }

  clear() {
    this.items = [];
    this.cartDiscounts = [];
  }
}
```

### POS System Integration
```javascript
// pos-system.js
class POSSystem {
  constructor() {
    this.discountManager = new DiscountManager('./database/pos.db');
    this.cart = new ShoppingCart(this.discountManager);
    this.scanner = new BarcodeScanner();

    this.setupScannerEvents();
  }

  setupScannerEvents() {
    this.scanner.onData(async (barcode) => {
      try {
        await this.cart.addItem(barcode);
        this.updateUI();
        this.playBeep(); // Audio feedback
      } catch (error) {
        console.error('Error processing barcode:', error);
        this.showError('Invalid barcode or product not found');
      }
    });
  }

  updateUI() {
    // Update cart display
    const cartDisplay = document.getElementById('cart-items');
    cartDisplay.innerHTML = '';

    this.cart.items.forEach(item => {
      const itemElement = document.createElement('div');
      itemElement.className = 'cart-item';

      let discountInfo = '';
      if (item.discountAmount > 0) {
        discountInfo = `<span class="discount">-$${item.discountAmount.toFixed(2)}</span>`;
      }

      itemElement.innerHTML = `
        <div class="item-name">${item.name}</div>
        <div class="item-details">
          ${item.quantity} x $${item.price.toFixed(2)} = $${(item.quantity * item.price).toFixed(2)}
          ${discountInfo}
        </div>
        <div class="item-total">$${(item.quantity * item.finalPrice).toFixed(2)}</div>
      `;

      cartDisplay.appendChild(itemElement);
    });

    // Update totals
    document.getElementById('subtotal').textContent = `$${this.cart.getSubtotal().toFixed(2)}`;
    document.getElementById('discount').textContent = `-$${this.cart.getTotalDiscount().toFixed(2)}`;
    document.getElementById('total').textContent = `$${this.cart.getTotal().toFixed(2)}`;
  }

  playBeep() {
    const audio = new Audio('beep.wav');
    audio.play();
  }

  showError(message) {
    const errorDiv = document.getElementById('error-message');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';

    setTimeout(() => {
      errorDiv.style.display = 'none';
    }, 3000);
  }
}
```

## Installation & Setup

### Prerequisites
```bash
# Node.js 16+ required
node --version

# Install Electron globally
npm install -g electron

# Create new Electron project
mkdir electron-pos-system
cd electron-pos-system
npm init -y
```

### Package Installation
```bash
# Core Electron dependencies
npm install --save electron

# Hardware integration packages
npm install --save \
  node-hid \
  serialport \
  usb-detection \
  escpos \
  escpos-usb \
  escpos-serialport \
  node-webcam \
  quagga \
  sqlite3

# Development dependencies
npm install --save-dev \
  electron-builder \
  nodemon
```

### Project Structure
```
electron-pos-system/
├── main.js                 # Electron main process
├── renderer.js             # Renderer process
├── index.html             # Main UI
├── package.json           # Dependencies
├── hardware/              # Hardware integration modules
│   ├── barcode-scanner.js
│   ├── cash-drawer.js
│   ├── thermal-printer.js
│   ├── camera-manager.js
│   └── discount-manager.js
├── database/              # Database files
│   └── pos.db
├── assets/                # Images, sounds, etc.
└── dist/                  # Built application
```

### Main Process Setup (main.js)
```javascript
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// Hardware modules
const BarcodeScanner = require('./hardware/barcode-scanner');
const CashDrawer = require('./hardware/cash-drawer');
const ThermalPrinter = require('./hardware/thermal-printer');
const DiscountManager = require('./hardware/discount-manager');

let mainWindow;
let scanner;
let cashDrawer;
let printer;
let discountManager;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  mainWindow.loadFile('index.html');

  // Initialize hardware
  initializeHardware();
}

async function initializeHardware() {
  try {
    // Initialize discount manager
    discountManager = new DiscountManager('./database/pos.db');

    // Initialize barcode scanner
    scanner = new BarcodeScanner();
    await scanner.connect(0x05e0, 0x1200); // Adjust vendor/product IDs

    scanner.onData((barcode) => {
      mainWindow.webContents.send('barcode-scanned', barcode);
    });

    // Initialize cash drawer
    cashDrawer = new CashDrawer('COM1'); // Adjust port

    // Initialize thermal printer
    printer = new ThermalPrinter('usb', {
      vendorId: 0x04b8,
      productId: 0x0202
    });
    await printer.connect();

    console.log('Hardware initialized successfully');
  } catch (error) {
    console.error('Hardware initialization failed:', error);
  }
}

// IPC handlers
ipcMain.handle('open-cash-drawer', async () => {
  try {
    await cashDrawer.openDrawer();
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('print-receipt', async (event, receiptData) => {
  try {
    await printer.printReceipt(receiptData);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('identify-barcode', async (event, barcode) => {
  try {
    const result = await discountManager.identifyBarcode(barcode);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  // Cleanup hardware connections
  if (scanner) scanner.disconnect();
  if (cashDrawer) cashDrawer.close();
  if (printer) printer.disconnect();

  if (process.platform !== 'darwin') {
    app.quit();
  }
});
```

## Configuration

### Hardware Configuration File (config.json)
```json
{
  "hardware": {
    "barcodeScanner": {
      "type": "usb",
      "vendorId": "0x05e0",
      "productId": "0x1200",
      "enabled": true
    },
    "cashDrawer": {
      "type": "serial",
      "port": "COM1",
      "baudRate": 9600,
      "enabled": true
    },
    "printer": {
      "type": "usb",
      "vendorId": "0x04b8",
      "productId": "0x0202",
      "enabled": true
    },
    "camera": {
      "enabled": true,
      "resolution": {
        "width": 1280,
        "height": 720
      }
    }
  },
  "store": {
    "name": "My Store",
    "address": "123 Main St, City, State 12345",
    "phone": "(*************",
    "taxRate": 0.08
  },
  "receipt": {
    "printLogo": true,
    "logoPath": "./assets/logo.png",
    "footerMessage": "Thank you for your business!"
  }
}
```

### Environment Setup
```bash
# Windows - Install USB drivers
# Download and install vendor-specific drivers for your hardware

# Linux - Add user to dialout group for serial access
sudo usermod -a -G dialout $USER

# macOS - Install Homebrew and dependencies
brew install node
```

## Troubleshooting

### Common Issues

#### 1. Scanner Not Detected
```javascript
// Debug scanner connection
const HID = require('node-hid');

function debugScanner() {
  console.log('Available HID devices:');
  const devices = HID.devices();
  devices.forEach(device => {
    console.log(`VID: 0x${device.vendorId.toString(16)}, PID: 0x${device.productId.toString(16)}, Product: ${device.product}`);
  });
}

debugScanner();
```

#### 2. Serial Port Issues
```javascript
// List available serial ports
const { SerialPort } = require('serialport');

async function listPorts() {
  try {
    const ports = await SerialPort.list();
    console.log('Available serial ports:');
    ports.forEach(port => {
      console.log(`${port.path} - ${port.manufacturer}`);
    });
  } catch (error) {
    console.error('Error listing ports:', error);
  }
}

listPorts();
```

#### 3. Printer Connection Problems
```javascript
// Test printer connection
async function testPrinter() {
  const printer = new ThermalPrinter('usb');

  try {
    await printer.connect();
    await printer.testPrint();
    console.log('Printer test successful');
  } catch (error) {
    console.error('Printer test failed:', error);
  } finally {
    printer.disconnect();
  }
}
```

#### 4. Permission Issues (Linux/macOS)
```bash
# Add udev rules for USB devices (Linux)
sudo nano /etc/udev/rules.d/99-pos-hardware.rules

# Add lines like:
SUBSYSTEM=="usb", ATTR{idVendor}=="04b8", ATTR{idProduct}=="0202", MODE="0666"

# Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger
```

### Hardware Compatibility

#### Supported Barcode Scanners
- **USB HID**: Most USB barcode scanners work as keyboard input
- **Serial**: RS232 scanners with configurable baud rates
- **Bluetooth**: Requires additional pairing setup

#### Supported Printers
- **Epson TM Series**: TM-T88V, TM-T88VI, TM-T20, TM-T82
- **Star Micronics**: TSP100, TSP650, TSP700
- **Citizen**: CT-S310, CT-S4000
- **Generic ESC/POS**: Most thermal printers supporting ESC/POS

#### Supported Cash Drawers
- **Serial**: Standard RJ11/RJ12 connection
- **USB**: USB-powered drawers
- **Printer-driven**: Connected through printer port

### Performance Optimization

#### 1. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_discounts_active ON discounts(is_active, start_date, end_date);
CREATE INDEX idx_product_discounts_product ON product_discounts(product_id);
```

#### 2. Memory Management
```javascript
// Implement connection pooling for hardware
class HardwareManager {
  constructor() {
    this.connections = new Map();
    this.connectionPool = {
      maxConnections: 5,
      timeout: 30000
    };
  }

  async getConnection(deviceType, config) {
    const key = `${deviceType}_${JSON.stringify(config)}`;

    if (this.connections.has(key)) {
      return this.connections.get(key);
    }

    // Create new connection
    const connection = await this.createConnection(deviceType, config);
    this.connections.set(key, connection);

    return connection;
  }

  cleanup() {
    this.connections.forEach(connection => {
      if (connection.disconnect) {
        connection.disconnect();
      }
    });
    this.connections.clear();
  }
}
```

### Hardware Port and Connection Details

#### Common Hardware Ports and Interfaces

| Hardware Type | Connection Type | Port/Interface | Typical Settings |
|---------------|----------------|----------------|------------------|
| **Barcode Scanner** | USB HID | USB 2.0/3.0 | Plug & Play |
| **Barcode Scanner** | Serial | COM1-COM9 (Windows) | 9600 baud, 8N1 |
| **Barcode Scanner** | PS/2 | Keyboard port | Legacy systems |
| **Receipt Printer** | USB | USB 2.0 | ESC/POS protocol |
| **Receipt Printer** | Serial | COM1-COM9 | 9600/19200 baud |
| **Receipt Printer** | Ethernet | RJ45 | IP: 192.168.x.x |
| **Cash Drawer** | Serial | COM port | 9600 baud, 8N1 |
| **Cash Drawer** | USB | USB port | HID or Serial over USB |
| **Cash Drawer** | Printer-driven | RJ11/RJ12 | Connected to printer |
| **Line Display** | USB | USB port | Serial over USB |
| **Line Display** | Serial | COM port | 9600 baud, 8N1 |
| **USB Camera** | USB | USB 2.0/3.0 | UVC compatible |

#### ESC/POS Command Reference

```javascript
// Common ESC/POS commands for cash drawer and printer
const ESC_POS_COMMANDS = {
  // Cash drawer commands
  OPEN_DRAWER: [0x1B, 0x70, 0x00, 0x19, 0x19], // ESC p 0 25 25
  DRAWER_STATUS: [0x1B, 0x75, 0x00],            // ESC u 0

  // Printer commands
  INITIALIZE: [0x1B, 0x40],                      // ESC @
  CUT_PAPER: [0x1D, 0x56, 0x00],                // GS V 0
  FEED_LINE: [0x0A],                             // LF
  CARRIAGE_RETURN: [0x0D],                       // CR

  // Text formatting
  BOLD_ON: [0x1B, 0x45, 0x01],                  // ESC E 1
  BOLD_OFF: [0x1B, 0x45, 0x00],                 // ESC E 0
  UNDERLINE_ON: [0x1B, 0x2D, 0x01],             // ESC - 1
  UNDERLINE_OFF: [0x1B, 0x2D, 0x00],            // ESC - 0

  // Alignment
  ALIGN_LEFT: [0x1B, 0x61, 0x00],               // ESC a 0
  ALIGN_CENTER: [0x1B, 0x61, 0x01],             // ESC a 1
  ALIGN_RIGHT: [0x1B, 0x61, 0x02],              // ESC a 2

  // Character size
  NORMAL_SIZE: [0x1D, 0x21, 0x00],              // GS ! 0
  DOUBLE_HEIGHT: [0x1D, 0x21, 0x01],            // GS ! 1
  DOUBLE_WIDTH: [0x1D, 0x21, 0x10],             // GS ! 16
  DOUBLE_SIZE: [0x1D, 0x21, 0x11]               // GS ! 17
};
```

#### Hardware Vendor IDs and Product IDs

```javascript
// Common hardware vendor/product IDs
const HARDWARE_IDS = {
  // Barcode scanners
  HONEYWELL_SCANNER: { vendorId: 0x0c2e, productId: 0x0b61 },
  SYMBOL_SCANNER: { vendorId: 0x05e0, productId: 0x1200 },
  DATALOGIC_SCANNER: { vendorId: 0x05f9, productId: 0x4204 },

  // Thermal printers
  EPSON_TM_T88V: { vendorId: 0x04b8, productId: 0x0202 },
  EPSON_TM_T20: { vendorId: 0x04b8, productId: 0x0e15 },
  STAR_TSP100: { vendorId: 0x0519, productId: 0x0001 },
  CITIZEN_CT_S310: { vendorId: 0x1d90, productId: 0x2168 },

  // Cash drawers
  APG_CASH_DRAWER: { vendorId: 0x0b05, productId: 0x0001 },
  STAR_CASH_DRAWER: { vendorId: 0x0519, productId: 0x0002 },

  // Line displays
  LOGIC_CONTROLS_PD3000: { vendorId: 0x0fa8, productId: 0x0030 },
  EPSON_DM_D110: { vendorId: 0x04b8, productId: 0x0e28 }
};
```

## Receipt Format Analysis from Current System

Based on your current C# POS system analysis, the receipt format includes:

### Standard Receipt Layout
```
================================
        STORE NAME
    123 Main St, City, ST
      Phone: (*************
================================
Date: MM/DD/YYYY  Time: HH:MM:SS
Receipt #: R1234567890
Cashier: John Doe
================================

Product Name 1
  2 x $10.50 = $21.00
    Discount: -$2.10

Product Name 2
  1 x $25.00 = $25.00

================================
Subtotal:           $46.00
Discount:           -$2.10
Taxable Amount:     $43.90
Tax (8.0%):         $3.51
================================
TOTAL:              $47.41

Payment Method: Cash
Amount Paid:        $50.00
Change:             $2.59
================================
   Thank you for shopping!
      Please come again
================================

[BARCODE: Receipt tracking code]

```

### Receipt Formatting Implementation
```javascript
// receipt-template.js
class ReceiptTemplate {
  constructor(config) {
    this.config = config;
    this.width = 32; // Characters per line for thermal printer
  }

  formatLine(text, align = 'left') {
    const padding = this.width - text.length;

    switch (align) {
      case 'center':
        const leftPad = Math.floor(padding / 2);
        const rightPad = padding - leftPad;
        return ' '.repeat(leftPad) + text + ' '.repeat(rightPad);
      case 'right':
        return ' '.repeat(padding) + text;
      default:
        return text + ' '.repeat(padding);
    }
  }

  formatCurrency(amount) {
    return `$${amount.toFixed(2)}`;
  }

  formatItemLine(name, quantity, price, total) {
    const line1 = this.formatLine(name);
    const qtyPrice = `${quantity} x ${this.formatCurrency(price)}`;
    const totalStr = this.formatCurrency(total);
    const line2 = this.formatLine(`  ${qtyPrice} = ${totalStr}`, 'left');
    return [line1, line2];
  }

  generateReceipt(data) {
    const lines = [];

    // Header
    lines.push(this.formatLine('='.repeat(this.width)));
    lines.push(this.formatLine(data.storeName, 'center'));
    if (data.storeAddress) {
      lines.push(this.formatLine(data.storeAddress, 'center'));
    }
    if (data.storePhone) {
      lines.push(this.formatLine(data.storePhone, 'center'));
    }
    lines.push(this.formatLine('='.repeat(this.width)));

    // Transaction info
    lines.push(this.formatLine(`Date: ${data.date}  Time: ${data.time}`));
    lines.push(this.formatLine(`Receipt #: ${data.receiptNumber}`));
    if (data.cashier) {
      lines.push(this.formatLine(`Cashier: ${data.cashier}`));
    }
    lines.push(this.formatLine('='.repeat(this.width)));
    lines.push('');

    // Items
    data.items.forEach(item => {
      const itemLines = this.formatItemLine(
        item.name,
        item.quantity,
        item.price,
        item.total
      );
      lines.push(...itemLines);

      // Show discount if applicable
      if (item.discountAmount > 0) {
        lines.push(this.formatLine(`    Discount: -${this.formatCurrency(item.discountAmount)}`));
      }
      lines.push('');
    });

    // Totals
    lines.push(this.formatLine('='.repeat(this.width)));
    lines.push(this.formatLine(`Subtotal:${this.formatCurrency(data.subtotal)}`, 'right'));

    if (data.totalDiscount > 0) {
      lines.push(this.formatLine(`Discount:-${this.formatCurrency(data.totalDiscount)}`, 'right'));
      lines.push(this.formatLine(`Taxable Amount:${this.formatCurrency(data.subtotal - data.totalDiscount)}`, 'right'));
    }

    lines.push(this.formatLine(`Tax (${(data.taxRate * 100).toFixed(1)}%):${this.formatCurrency(data.tax)}`, 'right'));
    lines.push(this.formatLine('='.repeat(this.width)));
    lines.push(this.formatLine(`TOTAL:${this.formatCurrency(data.total)}`, 'right'));
    lines.push('');

    // Payment info
    lines.push(this.formatLine(`Payment Method: ${data.paymentMethod}`));
    lines.push(this.formatLine(`Amount Paid:${this.formatCurrency(data.amountPaid)}`, 'right'));
    lines.push(this.formatLine(`Change:${this.formatCurrency(data.change)}`, 'right'));
    lines.push(this.formatLine('='.repeat(this.width)));

    // Footer
    lines.push(this.formatLine('Thank you for shopping!', 'center'));
    lines.push(this.formatLine('Please come again', 'center'));
    lines.push(this.formatLine('='.repeat(this.width)));

    return lines.join('\n');
  }
}
```

## Conclusion

This comprehensive guide provides a complete migration path from your existing C# POS system to Electron.js while maintaining all hardware functionality. The modular architecture allows for easy maintenance and future hardware additions.

### Key Benefits of Electron.js Migration:
- **Cross-platform compatibility**: Windows, macOS, Linux
- **Modern web technologies**: HTML5, CSS3, JavaScript
- **Rich ecosystem**: NPM packages for hardware integration
- **Easier maintenance**: Web-based UI development
- **Future-proof**: Regular updates and community support

### Hardware Integration Summary:

#### **USB Camera Integration**
- Web API implementation using `getUserMedia`
- Node.js library option with `node-webcam`
- Image capture and processing functionality

#### **Barcode Scanner Integration**
- **USB HID scanners** using `node-hid` library
- **Serial scanners** using `serialport` library
- **Camera-based scanning** using `quagga` library
- Support for multiple barcode formats (Code128, EAN, UPC, Code39, etc.)

#### **Cash Drawer Integration**
- **Serial communication** with ESC/POS commands
- **USB communication** using HID protocol
- **Network/Ethernet** drawer support
- Automatic opening during checkout process

#### **Receipt Printer Integration**
- **ESC/POS thermal printers** using `escpos` library
- **Receipt formatting** with store info, items, discounts, totals
- **Barcode printing** on receipts
- **Logo/image printing** capabilities
- Support for Epson, Star, Citizen, and generic ESC/POS printers

#### **Barcode Discount System**
- **Database schema** for products, discounts, and promotional codes
- **Discount types**: Percentage, fixed amount, buy-X-get-Y
- **Real-time discount application** during scanning
- **Cart-level and item-level** discount support

### Next Steps:
1. Set up development environment with Node.js and Electron
2. Install required hardware integration packages
3. Test hardware connections with provided code examples
4. Implement core POS functionality using the provided classes
5. Migrate existing database structure to SQLite
6. Configure hardware settings in config.json
7. Test thoroughly with actual hardware devices
8. Deploy and train users on the new system

### Hardware Requirements:
- **Operating System**: Windows 10+, macOS 10.14+, or Linux
- **Node.js**: Version 16 or higher
- **USB Ports**: For scanners, printers, cameras, and drawers
- **Serial Ports**: For legacy cash drawers and printers
- **Network**: For Ethernet-enabled devices

This guide ensures that your Electron.js POS system will have the exact same hardware functionality as your current C# system, with improved cross-platform compatibility and modern web-based user interface capabilities.

For additional support or specific hardware integration questions, refer to the individual hardware manufacturer documentation and the respective NPM package documentation.
