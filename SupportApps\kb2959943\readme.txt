KB2959943 - Update for Microsoft POS for .NET (1.14)

------------------------------------

KB2959943 

This update resolves issues that may occur when application(s) try to claim more than one device.

------
MORE INFORMATION

If more than one device is claimed in a single or multiple applications there is an exception thrown:  POSControlException ErrorCode(Timeout) ExtendedErrorCode(0) occurred: Another application has exclusive access to the device and did not relinquish control before timeout milliseconds expired.

Background:
An application, or even multiple applications need to be able to claim independent devices at the same time.  For example, a retail device that has both a scanner and printer, or other common combination, cannot claim both devices at the same time, without relinquishing one first.  Applications need to be able to keep claims to unique devices, while others are in use.

Fix:
The fix enables application(s) to claim multiple devices at the same time.

------
SYSTEM REQUIREMENTS

Microsoft POS for .NET (v1.14)

------
INSTALLATION INSTRUCTIONS

This update is installed using the MsiExec program. For example:

	msiexec /p KB2959943.msp /l* <logfile>

/l* <logfile>  Writes logging information into a logfile at the specified 
existing path. The path to the logfile location must already exist. "*" is a 
Wildcard, used to log all information except for the verbose and Extra debugging 
information options. To include the v and x options, specify "/l*vx".
