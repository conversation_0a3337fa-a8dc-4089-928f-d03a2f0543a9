﻿<html DIR="LTR" xmlns:tool="http://www.microsoft.com/tooltip" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ddue="http://ddue.schemas.microsoft.com/authoring/2003/5" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
  <head>
    <META HTTP-EQUIV="Content-Type" CONTENT="text/html; CHARSET=utf-8" />
    <META NAME="save" CONTENT="history" />
    <title>POS for .NET v1.12 Release Notes</title>
    
 <Style TYPE="text/css">

body
{
    background: #FFFFFF;
    color: #000000;
    font-family:    Verdana;
    font-size: medium;
    font-style: normal;
    font-weight: normal;
    margin-top: 0;
    margin-bottom:  0;
    margin-left:    0;
    margin-right:   0;
    width:  100%;
}

div.#mainSection
{
    font-size: 70%;
    width: 100%;
    padding-left:    10;
    margin-right: 10;
}

div.#mainBody
{
    font-size: 90%;
    margin-top: 10;
    padding-bottom: 20;
}

div.#header
{
    background-color: #D2D2D2;
    padding-top:    0;
    padding-bottom: 0;
    padding-left:   10;
    padding-right:  0;
    width:          100%;
}

div.#header table
{
    border-bottom-color: #C8CDDE;
    border-bottom-style: solid;
    border-bottom-width: 1;
    width:  100%;
}

span.#runningHeaderText
{
    color: #003399;
    font-size: 90%;
}

span.#nsrTitle
{
/*    color: #003399;*/
    font-size: 120%;
    font-weight: 600;
}

div.#header table td
{
    color: #000000;
    font-size: 70%;
    margin-top: 0;
    margin-bottom:  0;
    padding-right: 20;
}

div.#header table tr.#headerTableRow3 td
{
    padding-bottom: 2;
    padding-top: 5;
}

div.#header table.#bottomTable
{
    border-top-color: #FFFFFF;
    border-top-style: solid;
    border-top-width: 1;
    text-align: left;
}

div.#footer
{
    font-size: 90%;
    margin-top: 0;
    margin-bottom:  0;
    margin-left:    -5;
    margin-right:   0;
    padding-top:    2;
    padding-bottom: 2;
    padding-left:   0;
    padding-right:  0;
    width:  100%;
}

hr.#footerHR
{
    border-bottom-color: #EEEEFF;
    border-bottom-style: solid;
    border-bottom-width: 1;
    border-top-color: C8CDDE;
    border-top-style: solid;
    border-top-width: 1;
    height: 3;
    color: #D2D2D2;
}

div.section
{
    padding-top:    2;
    padding-bottom: 2;
    padding-right:  15;
    width:  100%;
}

.heading
{
    color:          #000000;
    font-weight:    bold;
    margin-top:     18;
    margin-bottom:  8;
}

h1.heading
{
    color: #000000;
    font-size:  150%;
}

.subHeading
{
    color:          #000000;
    font-weight:    bold;
    font-size:      150%;
    margin-bottom:  4;
}

h2.subHeading
{
    color:          #000000;
    font-weight:    bold;
    font-size:      130%;
}
h3.subHeading
{
    color:  #000000;
    font-size: 125%;
    font-weight: bold;
}

h4.subHeading
{
    color: #000000;
    font-size: 110%;
    font-weight: bold;
}

h4.procedureHeading
{
    color: #000080;
    font-size: 110%;
    font-weight: bold;
}

h5.subHeading
{
    color: #000000;
    font-size: 100%;
    font-weight: bold;
}

img
{
    padding-bottom: 10;
}

img.toggle
{
    border: 0;
    margin-right: 5;
    padding-bottom: 10;
}

img.copyCodeImage
{
    border: 0;
    margin: 1;
    margin-right: 3;
    padding-bottom: 10;
}

img.downloadCodeImage
{
    border: 0;
    margin-right: 3;
    padding-bottom: 10;
}

img.viewCodeImage
{
    border: 0;
    margin-right: 3;
    padding-bottom: 10;
}

img.note
{
    border: 0;
    margin-right: 3;
    padding-bottom: 10;
}

img.#membersOptionsFilterImage
{
    border: 0;
    margin-left: 10;
    vertical-align: middle;
    padding-bottom: 10;
}

img.#toggleAllImage
{
    margin-left: 4;
    vertical-align: middle;
    padding-bottom: 10;
}

div.#mainSection table
{
    border: 0;
    font-size: 100%;
    width:  100%;
    margin-top: 5px;
    margin-bottom: 15px;
}

div.#mainSection table tr
{
    vertical-align: top;
}

div.#mainSection table th
{
    text-align: left;
    background: #D8D8D8;
    border-bottom-color: #D8D8D8;
    border-bottom-style: solid;
    border-bottom-width: 1;
    color: #000000;
    padding-left: 5;
    padding-right: 5;
}

div.#mainSection table td
{
    background: #F2F2F2;
    border-top-color: #D8D8D8;
    border-top-style: solid;
    border-top-width: 1;
    padding-left: 5;
    padding-right: 5;
}

div.#mainSection table td.imageCell
{
    white-space: nowrap;
}

div.code
{
	width: 98%;
}

div.code table
{
    border: 0;
    font-size: 95%;
    margin-bottom: 5;
    width: 100%
}

div.code table th
{   
    text-align: left;
    background: #D8D8D8;
    border-bottom-color: #D8D8D8;
    border-bottom-style: solid;
    border-bottom-width: 1;
    color: #000000;
    font-weight: bold;
    padding-left: 5;
    padding-right: 5;
}

div.code table td
{
    background: #CCCCCC;
    border-top-color: #D8D8D8;
    border-top-style: solid;
    border-top-width: 1;
    padding-left: 5;
    padding-right: 5;
    padding-top: 5;
}

div.alert
{
	margin-left: 10;
	width: 98%;
}

div.alert table
{
    border: 1;
    font-size: 100%;
    width:  100%;
    border: solid 1 #DEDFEF;
}

div.alert table th
{
    text-align: left;
    background: #D8D8D8;
    border-bottom-width: 0;
    color: #000000;
    padding-left: 5;
    padding-right: 5;
    border: solid 1 #DEDFEF;
}

div.alert table td
{
    background: #FFFFFF;
    border-top-color: #D8D8D8;
    border-top-style: solid;
    border-top-width: 1;
    padding-left: 5;
    padding-right: 5;
    border: solid 1 #DEDFEF;
}

span.copyCode
{
    color: #0000ff;
    font-size: 90%;
    font-weight: normal;
    cursor: hand;
    float: right;
    display: inline;
    text-align: right;
}

.downloadCode
{
    color: #0000ff;
    font-size: 90%;
    font-weight: normal;
    cursor: hand;
}

.viewCode
{
    color: #0000ff;
    font-size: 90%;
    font-weight: normal;
    cursor: hand;
}

div.code pre
{
    font-family:    Monospace, Courier New, Courier;
    font-size: 105%;
    color:  #000000;
}

code
{
    font-family:    Monospace, Courier New, Courier;
    font-size: 105%;
    color:  #000000;
}

dl
{
    margin-top: 0;
    padding-left:   1;
}

dd
{
    margin-bottom:  0;
    margin-left:    0;
    padding-left:   20;
}

dd p
{
    margin-top: 5;
}

ul
{
    margin-left: 17;
    list-style-type: disc;
}

ul ul
{
    margin-bottom: 4;
    margin-left: 17;
    margin-top: 3;
    list-style-type: disc;
}

ol
{
    margin-left: 24;
    list-style-type: decimal;
}

ol ol
{
    margin-left: 24;
    margin-top: 3;
    list-style-type: lower-alpha;
}

li
{
    margin-top: 0;
    margin-bottom: 0;
    padding-bottom: 0;
    padding-top: 0;
    margin-left: 5;
}

p
{
    margin-bottom: 15;
}

.tip
{
    color:  #0000FF;
    font-style: italic;
    cursor:hand;
    text-decoration:underline;
}

.math
{
    font-family: Times New Roman;
    font-size: 125%
}
.sourceCodeList
{
    font-family: Verdana;
    font-size: 90%; 
}

pre.viewCode
{
    width: 100%;
    overflow: auto;
}

li:hover table, li.over table
{
    background-color: #C0C0C0;
}

li:hover ul, li.over ul
{ 
    background-color: #d2d2d2;
    border: 1px solid #000;
    display: block;
}

</style>
  </head>
  <body>
    <!--Topic built:1/23/2008-->
<input type="hidden" id="userDataCache" class="userDataStyle">
    </input>
    <input type="hidden" id="hiddenScrollOffset">
    </input>
    <img id="dropDownImage" style="display:none; height:0; width:0;" src="../local/drpdown.gif">
      
    </img>
    <img id="dropDownHoverImage" style="display:none; height:0; width:0;" src="../local/drpdown_orange.gif">
      
    </img>
    <img id="collapseImage" style="display:none; height:0; width:0;" src="../local/collapse.gif">
      
    </img>
    <img id="expandImage" style="display:none; height:0; width:0;" src="../local/exp.gif">
      
    </img>
    <img id="collapseAllImage" style="display:none; height:0; width:0;" src="../local/collall.gif">
      
    </img>
    <img id="expandAllImage" style="display:none; height:0; width:0;" src="../local/expall.gif">
      
    </img>
    <img id="copyImage" style="display:none; height:0; width:0;" src="../local/copycode.gif">
      
    </img>
    <img id="copyHoverImage" style="display:none; height:0; width:0;" src="../local/copycodeHighlight.gif">
      
    </img>
    <div id="header">
      <table width="100%" id="topTable">
        <tr>
          <td align="left">
            <span id="nsrTitle">POS for .NET v1.12 Release Notes</span>
          </td>
          <td align="right">
            <span id="headfb" class="feedbackhead">
            </span>
          </td>
        </tr>
        <tr id="headerTableRow3">
          <td />
          <td align="right">
          </td>
        </tr>
      </table>
      </div>
    <div id="mainSection">
      <div id="mainBody">
        <div id="allHistory" class="saveHistory" onsave="saveAll()" onload="loadAll()">
        </div>
        <font color="DarkGray">1/23/2008 </font><p /> 
        <span id="changeHistory">
        </span>
    <p>This document contains information about the POS for .NET v1.12 release.</p>
  <h1 class="heading">Table of Contents</h1><div id="sectionSection0" class="section"><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <p xmlns="">
        <a href="#SupportedOSPlatforms">Supported OS Platforms</a>
      </p>
      <p xmlns="">
        <a href="#POSfor.NETUpdatedtoUPOSv1.12">POS for .NET Updated to UPOS v1.12</a>
      </p>
      <p xmlns="">
        <a href="#Posfor.NETVersionCompatibility">POS for .NET Version Compatibility</a>
      </p>
      <p xmlns="">
        <a href="#NETFrameworkCompatibilityIssues">.NET Framework Compatibility Issues</a>
      </p>
      <p xmlns="">
        <a href="#KnownIssues">Known Issues</a>
      </p>
      <p xmlns="">
        <a href="#ProductSupport">Product Support</a>
      </p>
    </content></div><a name="SupportedOSPlatforms"></a><h1 class="heading">Supported OS Platforms</h1><div id="sectionSection1" class="section"><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <p xmlns="">POS for .NET is supported on the following platforms:</p>
    </content><sections xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <h3 class="subHeading" xmlns="">Development Platforms</h3><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
          <p xmlns="">Windows XP Professional</p>
          <p xmlns="">Windows Server 2003 Service Pack 1</p>
          <p xmlns="">Windows Vista Business</p>
          <p xmlns="">Windows Vista Enterprise</p>
        </content></div>
      <h3 class="subHeading" xmlns="">Target Platforms (Production)</h3><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
          <p xmlns="">Windows Embedded for Point of Service (WEPOS)</p>
          <p xmlns="">Windows XP Professional Service Pack 2</p>
          <p xmlns="">Windows Vista Business</p>
          <p xmlns="">Windows Vista Enterprise</p>
          <p xmlns="">The <b>POS for .NET 1.12 Runtime</b> can be installed on target AND development platforms. </p>
          <p xmlns="">The <b>POS for .NET 1.12 SDK</b> can be installed only on development systems.</p>
          <p xmlns="">Running Setup upgrades existing systems if an earlier version of POS for .NET is installed.</p>
          <p xmlns="">Installing the SDK overwrites SDK source files. If you have changed these files and want to keep them, make sure that you make backup copies of the sample source code files before you install the SDK.</p>
        </content></div>
    </sections></div><a name="POSfor.NETUpdatedtoUPOSv1.12"></a><h1 class="heading">POS for .NET Updated to UPOS v1.12</h1><div id="sectionSection2" class="section"><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <p xmlns="">POS for .NET 1.12 supports the UPOS v1.12 Specification. For more information about the new features of UPOS v1.12, see the Release 1.12 section of the Appendix D of the UPOS v1.12 Specification. The UPOS v1.12 Specification can be found at this <a href="http://go.microsoft.com/fwlink/?LinkId=108503" alt=""><linkText xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">ARTS Web site</linkText></a>.</p>
      <p xmlns="">For information about new and updated POS device classes, see the topic What's New in POS for .NET 1.12 in the documentation.</p>
    </content></div><a name="Posfor.NETVersionCompatibility"></a><h1 class="heading">POS for .NET Version Compatibility</h1><div id="sectionSection3" class="section"><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <p xmlns="">We recommend using the latest version of POS for .NET. The installed version of POS for .NET can be determined by reading the following registry key: </p>
      <p xmlns="">HKLM/Software/POSfor.NET/Setup/ProductVersion.</p>
    </content><sections xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <h3 class="subHeading" xmlns="">Version Check During POS Application or Service Object Setup</h3><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
          <p xmlns="">When you install POS applications and Service Objects (SOs), it is important to check the version of POS for .NET and make sure that it is compatible. </p>
          <p xmlns="">For example, Service Objects written for UPOS 1.12 must check the platform for POS for .NET version 1.12.*.* or greater. If the current version is incompatible, the installer should block the installation and inform users that they have to install the latest version of POS for .NET. </p>
        </content></div>
    </sections></div><a name="NETFrameworkCompatibilityIssues"></a><h1 class="heading">.NET Framework Compatibility Issues</h1><div id="sectionSection4" class="section"><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <p xmlns="">POS for .NET 1.12 depends on the Microsoft .NET Framework version 2.0. For your application to work, you have to make sure it is loading the version 2.0 of the .NET Framework. There are several ways that you can achieve this:</p>
      <ul xmlns=""><li>
          Recompile your POS application against the .NET Framework 2.0.<br></br>
        </li><li>
          Have only the .NET Framework 2.0 installed on your computer.<br></br>
        </li><li>
          Add the following content to your application’s Config file (create the file if it does not exist):<br></br>
        </li></ul>
      <p xmlns="">&lt;?xml version ="1.0"?&gt;</p>
      <p xmlns="">&lt;configuration&gt;</p>
      <p xmlns="">  &lt;startup&gt;</p>
      <p xmlns="">    &lt;supportedRuntime version="v2.0.50727" /&gt;</p>
      <p xmlns="">  &lt;/startup&gt;</p>
      <p xmlns="">&lt;/configuration&gt;</p>
    </content></div><a name="KnownIssues"></a><h1 class="heading">Known Issues</h1><div id="sectionSection5" class="section"><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
    </content><sections xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <h3 class="subHeading" xmlns="">POS for .NET Log File Location</h3><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
        </content><sections xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
          <h4 class="subHeading" xmlns="">Problem</h4><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
              <p xmlns="">POS for .NET writes its log file according to a path that is specified in the registry entries. By default, the WMI provider process that uses POS for .NET writes the log to C:\Windows\Temp.</p>
            </content></div>
          <h4 class="subHeading" xmlns="">Solution</h4><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
              <p xmlns="">Inspect both locations. When you try to find the POS for .NET log, the location of the log file may not be intuitive. This is because the POS Device Manager Command Line Tool (POSDM) or another application that uses the WMI provider may have been used, changing the environment path (%TEMP%) that specifies the location of the log. The value of %TEMP% varies depending on the account. Logging should be enabled only when the developer is debugging an SO or an application. </p>
            </content></div>
        </sections></div>
      <h3 class="subHeading" xmlns="">PowerState - Ambiguous Reference</h3><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
          <p xmlns="">Developers who use this SDK may encounter the following problem when trying to compile existing code:</p>
        </content><sections xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
          <h4 class="subHeading" xmlns="">Problem</h4><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
              <p xmlns="">Error CS0104 - 'PowerState' is an ambiguous reference between <b>System.Windows.Forms.PowerState</b> and <b>Microsoft.PointOfService.PowerState</b>.</p>
            </content></div>
          <h4 class="subHeading" xmlns="">Cause</h4><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
              <p xmlns="">This error occurs because the .NET Framework version 2.0 has added a new enumeration, <b>System.Windows.Forms.PowerState</b>, that conflicts with <b>Microsoft.PointOfService.PowerState</b>.</p>
            </content></div>
          <h4 class="subHeading" xmlns="">Solution</h4><div class="subSection" xmlns=""><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
              <p xmlns="">Use the fully qualified name <b>Microsoft.PointOfService.PowerState</b>.</p>
            </content></div>
        </sections></div>
    </sections></div><a name="ProductSupport"></a><h1 class="heading">Product Support</h1><div id="sectionSection6" class="section"><content xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">
      <p xmlns="">Any additional issues that are found with this release of POS for .NET will be posted as Knowledge Base (KB) articles on the Microsoft Customer Support Web site. To find a list of articles, see this <a href="http://go.microsoft.com/fwlink/?LinkId=108501" alt=""><linkText xmlns="http://ddue.schemas.microsoft.com/authoring/2003/5">Microsoft Web site</linkText></a> and search for <b>"POS for .NET"</b> (include the quotation marks).</p>
    </content></div><!--[if gte IE 5]>
			<tool:tip element="seeAlsoToolTip" avoidmouse="false"/><tool:tip element="languageFilterToolTip" avoidmouse="false"/><tool:tip element="roleInfoSpan" avoidmouse="false"/>
		<![endif]--></div>
      <div id="footer">
        
			
			© 2008 Microsoft Corporation. All rights reserved.
		</a>
 	
      </div>
    </div>
  </body>
</html>