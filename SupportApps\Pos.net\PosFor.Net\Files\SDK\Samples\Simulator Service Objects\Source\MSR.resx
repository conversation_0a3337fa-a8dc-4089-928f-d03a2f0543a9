<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="tb1.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 25</value>
  </data>
  <data name="tb1.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="tb1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tb1.Name" xml:space="preserve">
    <value>tb1</value>
  </data>
  <data name="&gt;&gt;tb1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb1.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="tb2.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 48</value>
  </data>
  <data name="tb2.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb2.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;tb2.Name" xml:space="preserve">
    <value>tb2</value>
  </data>
  <data name="&gt;&gt;tb2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb2.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="tb3.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 71</value>
  </data>
  <data name="tb3.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb3.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;tb3.Name" xml:space="preserve">
    <value>tb3</value>
  </data>
  <data name="&gt;&gt;tb3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb3.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="tb4.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 94</value>
  </data>
  <data name="tb4.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb4.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;tb4.Name" xml:space="preserve">
    <value>tb4</value>
  </data>
  <data name="&gt;&gt;tb4.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb4.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="tb5.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 116</value>
  </data>
  <data name="tb5.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb5.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;tb5.Name" xml:space="preserve">
    <value>tb5</value>
  </data>
  <data name="&gt;&gt;tb5.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb5.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="tb6.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 138</value>
  </data>
  <data name="tb6.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb6.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;tb6.Name" xml:space="preserve">
    <value>tb6</value>
  </data>
  <data name="&gt;&gt;tb6.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb6.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="tb7.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 160</value>
  </data>
  <data name="tb7.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb7.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;tb7.Name" xml:space="preserve">
    <value>tb7</value>
  </data>
  <data name="&gt;&gt;tb7.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb7.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="tb8.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 182</value>
  </data>
  <data name="tb8.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb8.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;tb8.Name" xml:space="preserve">
    <value>tb8</value>
  </data>
  <data name="&gt;&gt;tb8.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb8.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="SwipeCardButton.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="SwipeCardButton.Location" type="System.Drawing.Point, System.Drawing">
    <value>278, 448</value>
  </data>
  <data name="SwipeCardButton.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="SwipeCardButton.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="SwipeCardButton.Text" xml:space="preserve">
    <value>Swipe Card</value>
  </data>
  <data name="&gt;&gt;SwipeCardButton.Name" xml:space="preserve">
    <value>SwipeCardButton</value>
  </data>
  <data name="&gt;&gt;SwipeCardButton.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;SwipeCardButton.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;SwipeCardButton.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="FailedSwipeButton.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="FailedSwipeButton.Location" type="System.Drawing.Point, System.Drawing">
    <value>278, 477</value>
  </data>
  <data name="FailedSwipeButton.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="FailedSwipeButton.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="FailedSwipeButton.Text" xml:space="preserve">
    <value>Failed Swipe</value>
  </data>
  <data name="&gt;&gt;FailedSwipeButton.Name" xml:space="preserve">
    <value>FailedSwipeButton</value>
  </data>
  <data name="&gt;&gt;FailedSwipeButton.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;FailedSwipeButton.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;FailedSwipeButton.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="tb9.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 204</value>
  </data>
  <data name="tb9.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb9.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;tb9.Name" xml:space="preserve">
    <value>tb9</value>
  </data>
  <data name="&gt;&gt;tb9.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb9.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="tb10.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 226</value>
  </data>
  <data name="tb10.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb10.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;tb10.Name" xml:space="preserve">
    <value>tb10</value>
  </data>
  <data name="&gt;&gt;tb10.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb10.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb10.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="tb11.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 248</value>
  </data>
  <data name="tb11.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb11.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;tb11.Name" xml:space="preserve">
    <value>tb11</value>
  </data>
  <data name="&gt;&gt;tb11.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb11.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb11.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="tb12.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 271</value>
  </data>
  <data name="tb12.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb12.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="&gt;&gt;tb12.Name" xml:space="preserve">
    <value>tb12</value>
  </data>
  <data name="&gt;&gt;tb12.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb12.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb12.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="tb13.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 293</value>
  </data>
  <data name="tb13.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb13.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;tb13.Name" xml:space="preserve">
    <value>tb13</value>
  </data>
  <data name="&gt;&gt;tb13.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb13.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb13.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="tb16.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 359</value>
  </data>
  <data name="tb16.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb16.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="&gt;&gt;tb16.Name" xml:space="preserve">
    <value>tb16</value>
  </data>
  <data name="&gt;&gt;tb16.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb16.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb16.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="tb15.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 337</value>
  </data>
  <data name="tb15.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb15.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="&gt;&gt;tb15.Name" xml:space="preserve">
    <value>tb15</value>
  </data>
  <data name="&gt;&gt;tb15.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb15.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb15.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="tb14.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 315</value>
  </data>
  <data name="tb14.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb14.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;tb14.Name" xml:space="preserve">
    <value>tb14</value>
  </data>
  <data name="&gt;&gt;tb14.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb14.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb14.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 26</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 49</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 72</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 94</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 116</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="label6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 138</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="label7.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 160</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="label8.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 182</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="label9.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 204</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label9.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 226</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label11.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 249</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="label12.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 271</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label13.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label13.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 293</value>
  </data>
  <data name="label13.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label13.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="label14.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label14.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 315</value>
  </data>
  <data name="label14.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label14.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label14.Name" xml:space="preserve">
    <value>label14</value>
  </data>
  <data name="&gt;&gt;label14.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label14.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label14.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label15.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label15.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 337</value>
  </data>
  <data name="label15.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label15.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="label15.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label15.Name" xml:space="preserve">
    <value>label15</value>
  </data>
  <data name="&gt;&gt;label15.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label15.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label15.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label16.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label16.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 358</value>
  </data>
  <data name="label16.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label16.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="label16.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label16.Name" xml:space="preserve">
    <value>label16</value>
  </data>
  <data name="&gt;&gt;label16.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label16.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label16.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="radioButton1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton1.Location" type="System.Drawing.Point, System.Drawing">
    <value>20, 23</value>
  </data>
  <data name="radioButton1.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 17</value>
  </data>
  <data name="radioButton1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="radioButton1.Text" xml:space="preserve">
    <value>BANK</value>
  </data>
  <data name="&gt;&gt;radioButton1.Name" xml:space="preserve">
    <value>radioButton1</value>
  </data>
  <data name="&gt;&gt;radioButton1.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;radioButton1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radioButton2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton2.Location" type="System.Drawing.Point, System.Drawing">
    <value>20, 46</value>
  </data>
  <data name="radioButton2.Size" type="System.Drawing.Size, System.Drawing">
    <value>62, 17</value>
  </data>
  <data name="radioButton2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="radioButton2.Text" xml:space="preserve">
    <value>AAMVA</value>
  </data>
  <data name="&gt;&gt;radioButton2.Name" xml:space="preserve">
    <value>radioButton2</value>
  </data>
  <data name="&gt;&gt;radioButton2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton2.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;radioButton2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>53, 431</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 79</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>CardType</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label17.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label17.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 380</value>
  </data>
  <data name="label17.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label17.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="label17.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label17.Name" xml:space="preserve">
    <value>label17</value>
  </data>
  <data name="&gt;&gt;label17.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label17.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label17.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label18.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label18.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 402</value>
  </data>
  <data name="label18.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label18.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="label18.Text" xml:space="preserve">
    <value>Suffix</value>
  </data>
  <data name="&gt;&gt;label18.Name" xml:space="preserve">
    <value>label18</value>
  </data>
  <data name="&gt;&gt;label18.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label18.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label18.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tb17.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 381</value>
  </data>
  <data name="tb17.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb17.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="&gt;&gt;tb17.Name" xml:space="preserve">
    <value>tb17</value>
  </data>
  <data name="&gt;&gt;tb17.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb17.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb17.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="tb18.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 403</value>
  </data>
  <data name="tb18.Size" type="System.Drawing.Size, System.Drawing">
    <value>312, 20</value>
  </data>
  <data name="tb18.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="&gt;&gt;tb18.Name" xml:space="preserve">
    <value>tb18</value>
  </data>
  <data name="&gt;&gt;tb18.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tb18.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tb18.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>512, 578</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Microsoft Msr Simulator</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>MsrSimulatorWindow</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>Microsoft.PointOfService.DeviceSimulators.SimulatorBase, Microsoft.PointOfService.DeviceSimulators, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
</root>