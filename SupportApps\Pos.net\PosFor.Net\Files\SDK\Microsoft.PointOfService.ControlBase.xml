﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
	<assembly>
		<name>Microsoft.PointOfService.ControlBase</name>
	</assembly>
	<members>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.CardType">
			<summary>Enumerates the possible card types recognized by POS for .NET.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CardType.Aamva">
			<summary>Card type is United States and Canadian Driver's License or ID Card.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CardType.Blank">
			<summary>Card type is intentionally blank.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CardType.Iso">
			<summary>ISO card type.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CardType.JisOne">
			<summary>JIS Type I card type.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CardType.JisTwo">
			<summary>JIS Type II card type.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CardType.Other">
			<summary>Card type other than the ones mentioned above.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CardType.Unknown">
			<summary>Unknown card type.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase">
			<summary>Exposes functionality required for support of cash drawer devices.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.#ctor">
			<summary>Creates an instance of the <see cref="CashDrawerBase"></see>.</summary>
		</member>
		<member name="E:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.OnDrawerStateChanged">
			<summary>The <see cref="CashDrawerBase"></see> class raises <see cref="OnDrawerStateChanged"></see> events to inform the service object that the value of the <see cref="CashDrawerProperties.DrawerOpened"></see> property has been updated and the application has been notified of a drawer status change.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.Close">
			<summary>Releases the device and its resources.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.Open">
			<summary>Opens a device for subsequent input/output processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.OpenDrawer">
			<summary>Physically opens the cash drawer.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.OpenDrawerImpl">
			<summary>Implemented by the service object to physically open the cash drawer.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.PreFireEvent(Microsoft.PointOfService.StatusUpdateEventArgs)">
			<summary>Updates the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.DrawerOpened"></see> property and informs applications using the cash drawer of the status change.</summary>
			<param name="posEvent">An instance of the <see cref="StatusUpdateEventArgs"></see> class, posEvent contains the event the service object queued to the application.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.WaitForDrawerClose(System.Int32,System.Int32,System.Int32,System.Int32)">
			<summary>The application calls <see cref="M:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.WaitForDrawerClose(System.Int32,System.Int32,System.Int32,System.Int32)"></see> to wait until the cash drawer is closed. </summary>
			<param name="beepDelay">Number of milliseconds between the sounding of beeper tones.</param>
			<param name="beepDuration">Number of milliseconds that the beep tone will sound.</param>
			<param name="beepTimeout">Number of milliseconds to wait before starting an alert beeper.</param>
			<param name="beepFrequency">Audio frequency of the alert beeper in hertz.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.CapStatus">
			<summary>Indicates that the drawer can report its status.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.CapStatusMultiDrawerDetect">
			<summary>Indicates that statuses unique to each drawer in multi-drawer configurations can be reported.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.DrawerOpened">
			<summary>Indicates whether the drawer is open.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.Properties">
			<summary>Returns an instance of the <see cref="CashDrawerProperties"></see> helper class, containing getters and setters for cash drawer and common properties.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.CashDrawerProperties">
			<summary><see cref="CashDrawerProperties"></see> is a helper class created by POS for .NET to simplify the retrieval and setting of property values for cash drawer devices.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CashDrawerProperties.CapStatus">
			<summary>Indicates that the drawer can report its status.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CashDrawerProperties.CapStatusMultiDrawerDetect">
			<summary>Indicates whether statuses unique to each drawer in multi-drawer configurations* can be reported.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CashDrawerProperties.DrawerOpened">
			<summary>Indicates whether the drawer is open.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.Cell">
			<summary>A helper class created by POS for .NET to simplify the update of the array of cells used by the <see cref="DisplayData"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.Cell.Attribute">
			<summary>Contains the text mode attribute used to display the data.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.Cell.Bitmap">
			<summary>If the Type is <see cref="CellType.BitMap"></see>, contains the characters to be displayed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.Cell.Character">
			<summary>If the Type is <see cref="CellType.Character"></see>, contains the characters to be displayed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.Cell.Column">
			<summary>Contains the column where the data is to be displayed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.Cell.PixelData">
			<summary>Contains the pixels to be displayed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.Cell.Row">
			<summary>Contains the row where the data is to be displayed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.Cell.Type">
			<summary>Contains the type of character, bitmap, or glyph being displayed.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.CellType">
			<summary>Defines the possible types of data that can be displayed on the line display device.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CellType.Bitmap">
			<summary>The cell to be displayed contains a bitmap.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CellType.Character">
			<summary>The cell to be displayed contains characters.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CellType.Empty">
			<summary>The cell to be displayed contains no data.</summary>
		</member>
		<member name="F:Microsoft.PointOfService.BaseServiceObjects.CellType.Glyph">
			<summary>The cell to be displayed contains a glyph.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase">
			<summary>Contains most of the functionality for support of check scanner devices.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.#ctor">
			<summary>Creates an instance of the <see cref="CheckScannerBase"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.BeginInsertion(System.Int32)">
			<summary>Initiates the document insertion process.</summary>
			<param name="timeout">Number of milliseconds before failing the method.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.BeginInsertionImpl(System.Int32)">
			<summary><see cref="BeginInsertionImpl"></see> must be implemented by the service object to place the check scanner in check insertion processing.</summary>
			<param name="timeout">Number of milliseconds before failing the method.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.BeginRemoval(System.Int32)">
			<summary>Initiates the document removal process.</summary>
			<param name="timeout">Number of milliseconds before failing the method.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.BeginRemovalImpl(System.Int32)">
			<summary><see cref="BeginRemovalImpl"></see> must be implemented by the service object to initiate document removal processing.</summary>
			<param name="timeout">Number of milliseconds before failing the method.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ClearImage(Microsoft.PointOfService.CheckImageClear)">
			<summary>Clears a specific image or all the images in the device memory.</summary>
			<param name="by">Indicates how the image file is to be located so that it can be removed from the storage.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ClearImageImpl(Microsoft.PointOfService.CheckImageClear)">
			<summary>The service object should override the default <see cref="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ClearImage(Microsoft.PointOfService.CheckImageClear)"></see> implementation only if the check scanner device supports storage of images.</summary>
			<param name="by">Indicates how the image file is to be located so that it can be removed from the storage.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ClearInputProperties">
			<summary>Resets the values of properties altered by <see cref="E:Microsoft.PointOfService.CheckScanner.DataEvent"></see> or <see cref="E:Microsoft.PointOfService.CheckScanner.ErrorEvent"></see>.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.DefineCropArea(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
			<summary>Establishes one or more cropping areas that can be applied to a scanned image.</summary>
			<param name="y">The starting Y-coordinate of the cropping area.</param>
			<param name="width">The value added to the X-coordinate in order to determine the X endpoint of the cropping area.</param>
			<param name="height">The value added to the Y-coordinate in order to determine the Y endpoint of the cropping area.</param>
			<param name="cropAreaId">The numeric identifier for the defined crop area.</param>
			<param name="x">The starting X-coordinate of the cropping area.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.EndInsertion">
			<summary>Ends the document insertion process.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.EndInsertionImpl">
			<summary><see cref="EndInsertionImpl"></see> must be implemented by the service object to terminate the check insertion process.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.EndRemoval">
			<summary>Ends the document removal or ejection mode.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.EndRemovalImpl">
			<summary><see cref="EndRemovalImpl"></see> must be implemented by the service object to terminate the document removal process.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.Open">
			<summary>Opens a device for subsequent input/output processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.PreFireEvent(Microsoft.PointOfService.DataEventArgs)">
			<summary>Called prior to the delivery of a <see cref="DataEvent"></see> event to the application.</summary>
			<param name="posEvent">An instance of the <see cref="DataEventArgs"></see> class, posEvent contains the event that was queued to the application.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.RetrieveImage(System.Int32)">
			<summary>Retrieves the most recently scanned image, which is resident in the working area memory, and to use it to populate the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ImageData"></see> property. </summary>
			<param name="cropAreaId">Identifier to specify the storage location of the crop area parameters to be applied to the most recently scanned image held in the working area memory of the device. If the value is <see cref="CropAreaEntireImage"></see> then the entire area of the most recently scanned image is retrieved.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.RetrieveMemory(Microsoft.PointOfService.CheckImageLocate)">
			<summary>Retrieves an image that was previously stored in memory and use it to set the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ImageData"></see> property.</summary>
			<param name="by">Indicates how the image file is to be located so that it can be retrieved from the device memory storage.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.RetrieveMemoryImpl(Microsoft.PointOfService.CheckImageLocate)">
			<summary>The service object should override the default implementation only if the check scanner device supports storage of images.</summary>
			<param name="by">Indicates how the image file is to be located so that it can be retrieved from the device memory storage.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ScanCheck">
			<summary>Returns a scanned image of the check, created from the raw data in the format specified by the value of the <see cref="ImageFormat"></see> property.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.StoreImage(System.Int32)">
			<summary>Stores an image or a cropped area of the image in the memory of the device.</summary>
			<param name="cropAreaId">Identifier to specify the storage location of the crop area parameters to be applied to the most recently scanned image held in the working area memory of the device. If the value is <see cref="CropAreaEntireImage"></see> then the entire area of the most recently scanned image is retrieved.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.StoreImageImpl(System.Drawing.Bitmap)">
			<summary>The service object should override the default implementation only if the check scanner device supports the storage of images.</summary>
			<param name="image">A bitmap.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapAutoContrast">
			<summary>Gets the Boolean value that indicates whether the contrast can be adjusted automatically. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapAutoGenerateFileId">
			<summary>Indicates whether the check scanner device can automatically create a file name for the captured image file.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapAutoGenerateImageTagData">
			<summary>Indicates whether the check scanner device can automatically generate tag data used in reference to the image file for the captured image.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapAutoSize">
			<summary>Indicates whether the height and width of the scanned document will be automatically placed in the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.DocumentHeight"></see> and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.DocumentWidth"></see> properties when the image is captured.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapColor">
			<summary>Indicates whether the check scanner device supports image formats other than bi-tonal.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapConcurrentMicr">
			<summary>Indicates whether a check’s MICR (Magnetic Ink Character Recognition) data can be captured during a check scanning cycle (single-pass scanning).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapContrast">
			<summary>Gets the Boolean value that indicates the ability of the device to lighten or darken the scanned image.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapDefineCropArea">
			<summary>Indicates whether the check scanner device supports a feature that allows cropping of areas of interest within the scan image area defined by the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.DocumentHeight"></see> and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.DocumentWidth"></see> properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapImageFormat">
			<summary>Indicates which image file formats are supported by the check scanner device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapImageTagData">
			<summary>Indicates whether the check scanner device can set tag data, as defined by the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ImageTagData"></see> property, to the image data file stored in the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ImageData"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapMicrDevice">
			<summary>Indicates whether the check scanner device supports a check MICR read function.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapStoreImageFiles">
			<summary>Indicates whether the check scanner device can store check images in its hardware memory by using the <see cref="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.StoreImage(System.Int32)"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CapValidationDevice">
			<summary>Indicates that a check doesn’t have to be removed from the check scanner device prior to performing validation printing.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.Color">
			<summary>Specifies the image scan mode for subsequent document scan operations. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ConcurrentMicr">
			<summary>Indicates whether the check scanner device can conduct a MICR read during a check-scanning cycle.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.Contrast">
			<summary>Gets or sets the contrast to adjust the darkness of the image.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.CropAreaCount">
			<summary>Holds the number of defined crop areas that may be applied to the captured image.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.DeviceEnabled">
			<summary>Gets or sets a Boolean value that indicates whether the device has been placed in an operational state.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.DocumentHeight">
			<summary>Holds the height of the document scanned, or the height of the document to be scanned. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.DocumentWidth">
			<summary>Holds the width of the document scanned, or the width of the document to be scanned. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.FileId">
			<summary>Holds the file name associated with the image data file. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.FileIndex">
			<summary>Holds the file location associated with the image data file when either the <see cref="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.StoreImage(System.Int32)"></see> or <see cref="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.RetrieveMemory(Microsoft.PointOfService.CheckImageLocate)"></see> methods are called. Its value is set prior to a DataEvent event being delivered to the application.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ImageData">
			<summary>Holds the image data after the <see cref="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.RetrieveImage(System.Int32)"></see> or <see cref="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.RetrieveMemory(Microsoft.PointOfService.CheckImageLocate)"></see> methods are called.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ImageFormat">
			<summary>Holds the data format of the image file that the device will use when it captures an image.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ImageMemoryStatus">
			<summary>Indicates the current memory availability status if the device has the ability to store multiple image files. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.ImageTagData">
			<summary>Holds a string that specifies the tag name for the captured image data. The string may be specified by the application or automatically generated by the check scanner device. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.MapMode">
			<summary>Holds the mapping mode of the printer the application wants to use. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.MaxCropAreas">
			<summary>Specifies the maximum number of crop areas the device can support.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.Properties">
			<summary>Holds an instance of the <see cref="CheckScannerProperties"></see> helper class, which contains getters and setters for <see cref="CheckScanner"></see> properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.Quality">
			<summary>Sets the resolution of the device when a scan image is to take place. The resolution value is defined as dpi (dots per inch). </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.QualityList">
			<summary>Defines the resolutions the check scanner device is capable of supporting.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerBase.RemainingImagesEstimate">
			<summary>Provides a “best guess” estimate of the remaining number of images that can be stored. </summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.CheckScannerImage">
			<summary>A helper class created by POS for .NET to simplify the retrieval of scanned images and setting of properties.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.CheckScannerImage.#ctor(System.Drawing.Bitmap,System.String,System.Int32,System.String)">
			<summary>Creates an instance of the <see cref="CheckScannerImage"></see> class with the specified image data, file ID, file index, and image tag data.</summary>
			<param name="fileIndex">Holds the file location associated with the image data file.</param>
			<param name="fileId">Holds the file name associated with the image data file. </param>
			<param name="imageData">Holds the image data</param>
			<param name="imageTagData">Holds a string that specifies the tag name for the captured image data.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerImage.FileId">
			<summary>Holds the file name associated with the image data file. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerImage.FileIndex">
			<summary>Holds the file location associated with the image data file.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerImage.ImageData">
			<summary>Holds the image data.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerImage.ImageTagData">
			<summary>Holds a string that specifies the tag name for the captured image data.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties">
			<summary>A helper class created by POS for .NET to simplify the retrieval and setting of property values for check scanner devices.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapAutoGenerateFileId">
			<summary>Indicates whether the check scanner device can automatically create a file name for the captured image file.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapAutoGenerateImageTagData">
			<summary>Indicates whether the check scanner device can automatically generate tag data used in reference to the image file for the captured image.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapAutoSize">
			<summary>Indicates that the height and width of the scanned document will be automatically placed in the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.DocumentHeight"></see> and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.DocumentWidth"></see> properties when the image is captured.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapColor">
			<summary>Indicates that the check scanner device supports image formats other than bi-tonal.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapConcurrentMicr">
			<summary>Indicates whether a check’s MICR (Magnetic Ink Character Recognition) data can be captured during a check scanning cycle (single-pass scanning).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapDefineCropArea">
			<summary>Indicates whether the check scanner device supports a feature that allows cropping of areas of interest within the scan image area defined by the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.DocumentHeight"></see> and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.DocumentWidth"></see> properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapImageFormat">
			<summary>Indicates whether the check scanner device supports a feature that allows cropping of areas of interest within the scan image area defined by the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.DocumentHeight"></see> and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.DocumentWidth"></see> properties. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapImageTagData">
			<summary>Indicates that the check scanner device can set tag data, as defined by the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.ImageTagData"></see> property, to the image data file stored in the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.ImageData"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapMicrDevice">
			<summary>Indicates that the check scanner device supports a check MICR read function.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapStoreImageFiles">
			<summary>Indicates that the check scanner device can store check images in its hardware memory.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.CapValidationDevice">
			<summary>Indicates that a check doesn’t have to be removed from the check scanner device prior to performing validation printing.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.Color">
			<summary>Specifies the image scan mode for subsequent document scan operations.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.ConcurrentMicr">
			<summary>Indicates whether the check scanner device can conduct a MICR read during a check-scanning cycle.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.DocumentHeight">
			<summary>Holds the height of the document scanned, or the height of the document to be scanned.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.DocumentWidth">
			<summary>Holds the width of the document scanned, or the width of the document to be scanned.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.FileId">
			<summary>Holds the file name associated with the image data file.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.FileIndex">
			<summary>Holds the file location associated with the image data file.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.ImageData">
			<summary>Holds the image data after the RetrieveImage or RetrieveMemory methods are called.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.ImageFormat">
			<summary>Holds the data format of the image file that the device will use when it captures an image.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.ImageMemoryStatus">
			<summary>Indicates the current memory availability status if the device has the ability to store multiple image files.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.ImageTagData">
			<summary>Holds a string that specifies the tag name for the captured image data. The string may be specified by the application or automatically generated by the check scanner device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.MapMode">
			<summary>Specifies the units of measure that are currently valid for the check scanner device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.MaxCropAreas">
			<summary>Specifies the maximum number of crop areas the device can support.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.Quality">
			<summary>Sets the resolution of the device when a scan image is to take place. The resolution value is defined as dpi (dots per inch).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.QualityList">
			<summary>Defines the resolutions the check scanner device is capable of supporting.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CheckScannerProperties.RemainingImagesEstimate">
			<summary>Provides a “best guess” estimate of the remaining number of images that can be stored.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.ClearPrintAreaOperation">
			<summary>Clears the area defined by the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.ClearPrintAreaOperation.PageModePrintArea"></see> property.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.CutPaperOperation">
			<summary>When the application updates printer properties and then calls the <see cref="CutPaper"></see> method, the <see cref="PosPrinterBase"></see> class uses an instance of the <see cref="CutPaperOperation"></see> class, cast to the <see cref="PrintOperation"></see> class and stored in the <see cref="PrintOperationCollection"></see> array, to capture information about that method call.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.CutPaperOperation.Percentage">
			<summary>Contains the value of the percentage parameter set when the application made the <see cref="CutPaper"></see> method call.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics">
			<summary>Helps a service object implement device statistics reporting.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.#ctor(System.String,System.String,System.String)">
			<summary>Initializes a new instance of DeviceStatistics with the specified device information.</summary>
			<param name="deviceType">Contains the device type.</param>
			<param name="devicePath">Contains the hardware path or any string that uniquely identifies the device.</param>
			<param name="deviceName">Contains the name of the device or of the performance object that exposes performance counters.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.Close">
			<summary>Releases the class and its resources.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.CreateStatistic(System.String,System.Boolean)">
			<summary>Creates and stores a new statistic for a device, after verifying that the statistic doesn't already exist.</summary>
			<param name="statisticName">The name of the statistic (or the constant that refers to the statistic) to be created.</param>
			<param name="resettable">If set to true, the created statistic will be identified by POS for .NET as resettable to 0 (zero).</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.CreateStatistic(System.String,System.Boolean,System.String)">
			<summary>Creates and stores a new statistic for a device, after verifying that the statistic doesn't already exist.</summary>
			<param name="statisticName">The name of the statistic (or the constant that refers to the statistic) to be created.</param>
			<param name="resettable">If set to true, the created statistic will be identified by POS for .NET as resettable to 0 (zero).</param>
			<param name="unitOfMeasure">The unit of measure.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.Dispose">
			<summary>Releases all managed and unmanaged resources.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.Dispose(System.Boolean)">
			<summary>Releases the specified resources.</summary>
			<param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.FlushStatistics">
			<summary>Saves the current statistics values to the XML statistics file.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.GetStatisticValue(System.String)">
			<summary>Returns the value of the specified statistic.</summary>
			<param name="name">The name of the statistic that will be read.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.IncrementStatistic(System.String)">
			<summary>Increments the specified statistic by 1.</summary>
			<param name="name">Name of the statistic to be incremented. For UPOS-defined statistics, name should contain the POS for .NET predefined constant for the statistic.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.IncrementStatistic(System.String,System.Int32)">
			<summary>Increments a statistic by the specified amount.</summary>
			<param name="increment">Amount by which to increment the statistic.</param>
			<param name="name">Name of the statistic to be incremented. For UPOS-defined statistics, name should contain the POS for .NET predefined constant for the statistic.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.LoadStatistics">
			<summary>Loads the saved statistics values for a device, so that they can be incremented during the current work session and then re-saved.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.ResetStatistic(System.String)">
			<summary>Resets the specified statistic to zero.</summary>
			<param name="statistic">Specifies the statistic that is to be reset.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.ResetStatistics">
			<summary>Resets all statistics associated with a device to 0 (zero).</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.ResetStatistics(Microsoft.PointOfService.StatisticCategories)">
			<summary>Resets all statistics for a specified category to 0 (zero).</summary>
			<param name="statistics">Contains the category of statistics the application wishes to reset for the device. Possible categories are defined by the <see cref="StatisticsCategories"></see> enumeration.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.ResetStatistics(System.String[])">
			<summary>Resets the specified statistics to 0 (zero).</summary>
			<param name="statistics">Contains a comma-separated string of statistics.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.RetrieveStatistic(System.String)">
			<summary>Retrieves all device statistics.</summary>
			<param name="statistic">Contains a comma-separated string of statistics.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.RetrieveStatistics">
			<summary>Retrieves all device statistics.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.RetrieveStatistics(Microsoft.PointOfService.StatisticCategories)">
			<summary>Retrieves the statistics for the specified category.</summary>
			<param name="statistics">Contains the category of statistics the application wishes to retrieve. Possible values are defined by the StatisticsCategories enumeration.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.RetrieveStatistics(System.String[])">
			<summary>Retrieves the specified string of statistics.</summary>
			<param name="statistics">Contains a comma-separated string of statistics.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.SetStatisticHandlers(System.String,Microsoft.PointOfService.BaseServiceObjects.GetStatistic,Microsoft.PointOfService.BaseServiceObjects.SetStatistic)">
			<summary>Sets the statistic handlers for an existing statistic.</summary>
			<param name="getStatistic">The handler to be called to get the statistic.</param>
			<param name="statisticName">Name of (or constant that refers to) the related statistic to be retrieved or set.</param>
			<param name="setStatistic">The handler to be called to set the statistic.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.SetStatisticValue(System.String,System.Object)">
			<summary>Sets the value of a specified statistic.</summary>
			<param name="name">Name of (or the constant that refers to) the statistic whose value is to be set.</param>
			<param name="value">The value to which the statistic should be set.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.UpdateStatistic(System.String,System.Object)">
			<summary>Updates the value of the specified statistic.</summary>
			<param name="name">Name of the statistic to be updated.</param>
			<param name="value">Value to which the statistic should be set.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.UpdateStatistics(Microsoft.PointOfService.Statistic[])">
			<summary>Updates a list of statistics with the corresponding specified values.</summary>
			<param name="statistics">Contains an array of <see cref="Statistic"></see> class instances (name-value pairs).</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.UpdateStatistics(Microsoft.PointOfService.StatisticCategories,System.Object)">
			<summary>Updates the specified category of statistics with the specified value.</summary>
			<param name="statistics">Contains the category of statistics the application wishes to update. Possible categories are defined by the <see cref="T:Microsoft.PointOfService.StatisticCategories"></see> enumeration.</param>
			<param name="value">Contains the value to be used to update the statistics in the specified category.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.CapStatisticsReporting">
			<summary>Indicates whether a device supports statistics reporting.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.CapUpdateStatistics">
			<summary>Indicates whether at least one statistic can reset its value to zero.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.DeviceStatistics.FlushInterval">
			<summary>Contains the interval used by POS for .NET to determine when to update the device's XML statistics file with the current statistics values.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.DrawerStateChangedEventArgs">
			<summary>Used as the event type for the <see cref="DrawerStateChangedEventHandler"></see> delegate.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.DrawerStateChangedEventHandler">
			<summary>Delegate for the <see cref="E:Microsoft.PointOfService.BaseServiceObjects.CashDrawerBase.OnDrawerStateChanged"></see> event.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.GetStatistic">
			<summary>Gets a device statistic.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase">
			<summary>Contains most of the functionality required for support of Line Display devices, including drawing of text and graphics (glyphs and bitmaps), window management, and scrolling, marquee, and teletype functions. </summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.#ctor">
			<summary>Creates an instance of the <see cref="LineDisplayBase"></see> class.</summary>
		</member>
		<member name="E:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.BlinkRateChangedEvent">
			<summary>Informs the service object that the value of the <see cref="BlinkRate"></see> property has changed.</summary>
		</member>
		<member name="E:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CurrentWindowChangedEvent">
			<summary>Informs the service object that the value of the <see cref="CurrentWindow"></see> property has changed.</summary>
		</member>
		<member name="E:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorTypeChangedEvent">
			<summary>Inform the service object that the value of the <see cref="CursorType"></see> property has changed.</summary>
		</member>
		<member name="E:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ScreenModeChangedEvent">
			<summary>Inform the service object that the value of the <see cref="ScreenMode"></see> property has changed.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ClearDescriptors">
			<summary>Turns off all descriptors.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ClearDescriptorsImpl">
			<summary>Called by the <see cref="LineDisplayBase.ClearDescriptors"></see> method.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ClearText">
			<summary>Clears the current window by displaying blanks, sets the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorRow"></see> and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorColumn"></see> properties to zero, and then resynchronizes the beginning of the window with the start of the viewport.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CreateWindow(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
			<summary>Creates a viewport over the portion of the display specified by the first four parameters.</summary>
			<param name="viewportRow">The viewport’s start device row.</param>
			<param name="viewportHeight">The number of device rows in the viewport.</param>
			<param name="viewportWidth">The number of device columns in the viewport.</param>
			<param name="viewportColumn">The viewport’s start device column.</param>
			<param name="windowWidth">The number of columns in the window.</param>
			<param name="windowHeight">The number of rows in the window.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DefineGlyph(System.Int32,System.Byte[])">
			<summary>Defines a glyph character.</summary>
			<param name="glyph">Byte array that defines the glyph.</param>
			<param name="glyphCode">The character code to be defined.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DestroyWindow">
			<summary>Destroys the current window.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayBitmap(System.String,System.Int32,System.Int32)">
			<summary>Displays the bitmap with the specified alignment.</summary>
			<param name="alignmentX">Horizontal placement of the bitmap to be displayed.</param>
			<param name="fileName">File name or URL of the bitmap file.</param>
			<param name="alignmentY">Vertical placement of the bitmap to be displayed.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayBitmap(System.String,System.Int32,System.Int32,System.Int32)">
			<summary>Displays the bitmap with the specified alignment and width.</summary>
			<param name="alignmentX">Horizontal placement of the bitmap to be displayed.</param>
			<param name="fileName">File name or URL of the bitmap file.</param>
			<param name="width">Width of the bitmap to be displayed.</param>
			<param name="alignmentY">Vertical placement of the bitmap to be displayed.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayData(Microsoft.PointOfService.BaseServiceObjects.Cell[])">
			<summary>Called by the <see cref="LineDisplayBase"></see> class to draw the window.</summary>
			<param name="cells">Contains an array of <see cref="Cell"></see> class instances, each of which represents a single character position on the display (or the set of pixels that occupy a single character position).</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayText(System.String)">
			<param name="data">The string of characters to display.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayText(System.String,Microsoft.PointOfService.DisplayTextMode)">
			<param name="data">The string of characters to display.</param>
			<param name="attribute">The display attribute for the text. Possible values are defined by the <see cref="DisplayTextMode"></see> enumeration. Valid values are Normal or Blink.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayTextAt(System.Int32,System.Int32,System.String)">
			<summary>Displays the string of characters at the specified row and column.</summary>
			<param name="data">The string of characters to display.</param>
			<param name="row">The start row for the text.</param>
			<param name="column">The start column for the text.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayTextAt(System.Int32,System.Int32,System.String,Microsoft.PointOfService.DisplayTextMode)">
			<summary>Displays the string of characters at the specified row and column, in the specified display mode.</summary>
			<param name="data">The string of characters to display.</param>
			<param name="row">The start row for the text.</param>
			<param name="column">The start column for the text.</param>
			<param name="attribute">The display attribute for the text. Possible values are defined by the <see cref="DisplayTextMode"></see> enumeration. Must be either <see cref="DisplayTextMode.Normal"></see> or <see cref="DisplayTextMode.Blink"></see>.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.Open">
			<summary>Creates the device window and sets its <see cref="DeviceBrightness"></see> property to 100.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ReadCharacterAtCursor">
			<summary>Reads the currently displayed character at the cursor position.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.RefreshWindow(System.Int32)">
			<summary>Changes the current window to the one specified by the window parameter, then redisplays that window’s viewport.</summary>
			<param name="window">Specifies the window to be refreshed.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ScrollText(Microsoft.PointOfService.DisplayScrollText,System.Int32)">
			<summary>Scrolls text in the current window, in the specified direction.</summary>
			<param name="direction">Indicates the scrolling direction, as defined by the <see cref="DisplayScrollText"></see> enumeration.</param>
			<param name="units">Indicates the number of columns or rows to scroll.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.SetBitmap(System.Int32,System.String,System.Int32,System.Int32)">
			<summary>Sets the bitmap number and placement.</summary>
			<param name="alignmentX">The horizontal placement of the bitmap.</param>
			<param name="fileName">The file name or URL of the bitmap file.</param>
			<param name="bitmapNumber">The number to be assigned to this bitmap.</param>
			<param name="alignmentY">The vertical placement of the bitmap.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.SetBitmap(System.Int32,System.String,System.Int32,System.Int32,System.Int32)">
			<summary>Sets the bitmap number, placement, and width.</summary>
			<param name="alignmentX">The horizontal placement of the bitmap.</param>
			<param name="fileName">The file name or URL of the bitmap file.</param>
			<param name="width">The width of the bitmap to be displayed.</param>
			<param name="bitmapNumber">The number to be assigned to this bitmap.</param>
			<param name="alignmentY">The vertical placement of the bitmap.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.SetDescriptor(System.Int32,Microsoft.PointOfService.DisplaySetDescriptor)">
			<summary>Sets the state of a descriptor.</summary>
			<param name="attribute">Indicates the attribute for the descriptor. Possible values are defined by the <see cref="DisplaySetDescriptor"></see> enumeration.</param>
			<param name="descriptor">Indicates which descriptor to change. The value can range between zero and one less than the value of the <see cref="DeviceDescriptors"></see> property.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.SetDescriptorImpl(System.Int32,Microsoft.PointOfService.DisplaySetDescriptor)">
			<summary>Called by the <see cref="LineDisplayBase.SetDescriptor"></see> method.</summary>
			<param name="attribute">Indicates the attribute for the descriptor. Possible values are defined by the <see cref="DisplaySetDescriptor"></see> enumeration.</param>
			<param name="descriptor">Indicates which descriptor to change. The value can range between zero and one less than the value of the <see cref="DeviceDescriptors"></see> property.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.BlinkRate">
			<summary>Holds the blink cycle in milliseconds.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapBitmap">
			<summary>If true, display of bitmaps is supported.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapBlink">
			<summary>Holds the character blink capability of the line display device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapBlinkRate">
			<summary>If true, the device’s blink rate can be controlled, and the application uses the <see cref="P:Microsoft.PointOfService.LineDisplay.BlinkRate"></see> property to indicate the rate at which the display blinks.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapBrightness">
			<summary>If true, brightness control is supported.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapCharacterSet">
			<summary>Holds the default character set capability of the line display device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapCursorType">
			<summary>Holds a bitwise indication of the cursor types supported by the line display device, selectable via the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorType"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapCustomGlyph">
			<summary>If true, the device supports custom glyph definition.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapDescriptors">
			<summary>If true, the device supports descriptors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapHMarquee">
			<summary>If true, the device supports horizontal marquee windows.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapICharWait">
			<summary>If true, the device supports inter-character wait.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapMapCharacterSet">
			<summary>If true, then the service object is able to map the characters provided by the application to the character sets defined in the <see cref="P:Microsoft.PointOfService.LineDisplay.CharacterSetList"></see> property when displaying data.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapReadBack">
			<summary>Holds the capability of the line display device to read back the data displayed on it.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapReverse">
			<summary>Holds the reverse video capability of the line display device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapScreenMode">
			<summary>If true, the line display device supports changing the screen mode (that is, the number of text rows and columns).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CapVMarquee">
			<summary>If true, the device supports vertical marquee windows.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CharacterSet">
			<summary>Holds the character set for displaying characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CharacterSetList">
			<summary>Holds a string of character set numbers.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.Columns">
			<summary>Holds the number of columns for this window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorColumn">
			<summary>Holds the column in the current window to which the next displayed character will be output.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorDeviceColumn">
			<summary>Translates the <see cref="CursorColumn"></see> value into a device coordinate by returning an integer that represents the value of the <see cref="CursorColumn"></see> property added to the current window’s start column value.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorDeviceRow">
			<summary>Translates the <see cref="CursorRow"></see> value into a device coordinate by returning an integer that represents the value of the <see cref="CursorRow"></see> property added to the current window’s start row value.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorRow">
			<summary>Holds the row in the current window to which the next displayed character will be output.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorType">
			<summary>Holds the cursor type for the current window. Possible values are defined by the <see cref="T:Microsoft.PointOfService.DisplayCursors"></see> enumeration.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorUpdate">
			<summary>If true when characters are displayed using the <see cref="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayText(System.String)"></see> and <see cref="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DisplayTextAt(System.Int32,System.Int32,System.String)"></see> methods, then the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorRow"></see> and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorColumn"></see> properties will be updated to point to the character beyond the last character output.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CustomGlyphList">
			<summary>Holds an array of instances of the <see cref="T:Microsoft.PointOfService.RangeOfCharacters"></see> helper class, which represents the character code ranges that are available for defining glyphs.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DeviceBrightness">
			<summary>Holds the device brightness value, expressed as a percentage between 0 and 100.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DeviceColumns">
			<summary>Holds the number of columns on this device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DeviceDescriptors">
			<summary>Holds the number of descriptors on this device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DeviceRows">
			<summary>Holds the number of rows on this device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.DeviceWindows">
			<summary>Holds the maximum number of windows supported by the line display device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.GlyphHeight">
			<summary>Indicates the glyph height based on the number of pixels for a character cell.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.GlyphWidth">
			<summary>Indicates the glyph width based on the number of pixels for a character cell.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.InterCharacterWait">
			<summary>Holds the wait time, specified in milliseconds, between the display of each character with the <see cref="M:Microsoft.PointOfService.LineDisplay.DisplayText(System.String)"></see> and <see cref="M:Microsoft.PointOfService.LineDisplay.DisplayTextAt(System.Int32,System.Int32,System.String)"></see> methods.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.MapCharacterSet">
			<summary>If true, then the service object is able to map the characters provided by the application to the character sets defined in the <see cref="P:Microsoft.PointOfService.LineDisplay.CharacterSetList"></see> property when displaying data.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.MarqueeFormat">
			<summary>Holds the marquee format for the current window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.MarqueeRepeatWait">
			<summary>Holds the wait time, specified in milliseconds, between scrolling the final character or row of the window into its viewport and restarting the marquee with the first or last character or row.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.MarqueeType">
			<summary>Holds the marquee type for the current window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.MarqueeUnitWait">
			<summary>Holds the wait time, specified in milliseconds, between marquee scrolling of each column or row in the window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.MaximumX">
			<summary>Contains the maximum number of horizontal pixels supported by the device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.MaximumY">
			<summary>Contains the maximum number of vertical pixels supported by the device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.Properties">
			<summary>Returns an instance of the <see cref="LineDisplayProperties"></see> helper class, containing getters and setters for <see cref="LineDisplay"></see> and common properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.Rows">
			<summary>Holds the number of rows for this window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ScreenMode">
			<summary>Holds the screen mode value of the device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ScreenModeList">
			<summary>Holds the array of <see cref="T:Microsoft.PointOfService.DisplayScreenMode"></see> class instances that are supported by the device.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.BlinkRateChangedEventHandler">
			<summary>Delegate for the <see cref="E:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.BlinkRateChangedEvent"></see> protected event. </summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CurrentWindowChangedEventHandler">
			<summary>Delegate for the <see cref="E:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CurrentWindowChangedEvent"></see> protected event. </summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorTypeChangedEventHandler">
			<summary>Delegate for the <see cref="E:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.CursorTypeChangedEvent"></see> protected event. </summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ScreenModeChangedEventHandler">
			<summary>Delegate for the <see cref="E:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBase.ScreenModeChangedEvent"></see> protected event. </summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBitmap">
			<summary>A helper class created by POS for .NET and used by the <see cref="LineDisplayBase"></see> class for creating and displaying bitmaps.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBitmap.Bitmap">
			<summary>Contains the bitmap to be displayed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayBitmap.FileName">
			<summary>Contains the filename of the bitmap to be displayed.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties">
			<summary>A helper class created by POS for .NET to simplify the retrieval and setting of property values for line display devices.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.BlinkRate">
			<summary>Holds the blink cycle in milliseconds.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapBitmap">
			<summary>If true, display of bitmaps is supported.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapBlink">
			<summary>Holds the character blink capability of the line display device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapBlinkRate">
			<summary>If true, the device’s blink rate can be controlled, and the application uses the <see cref="P:Microsoft.PointOfService.LineDisplay.BlinkRate"></see> property to indicate the rate at which the display blinks.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapBrightness">
			<summary>If true, brightness control is supported.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapCharacterSet">
			<summary>Holds the default character set capability of the line display device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapCursorType">
			<summary>Holds a bitwise indication of the cursor types supported by the line display device, selectable via the <see cref="P:Microsoft.PointOfService.LineDisplay.CursorType"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapCustomGlyph">
			<summary>If true, the device supports custom glyph definition.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapDescriptors">
			<summary>If true, the device supports descriptors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapHMarquee">
			<summary>If true, the device supports horizontal marquee windows.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapICharWait">
			<summary>If true, the device supports inter-character wait.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapMapCharacterSet">
			<summary>If true, then the service object is able to map the characters provided by the application to the character sets defined in the <see cref="P:Microsoft.PointOfService.LineDisplay.CharacterSetList"></see> property when displaying data.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapReadBack">
			<summary>Holds the capability of the line display device to read back the data displayed on it.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapReverse">
			<summary>Holds the reverse video capability of the line display device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapScreenMode">
			<summary>If true, the line display device supports changing the screen mode (that is, the number of text rows and columns).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CapVMarquee">
			<summary>If true, the device supports vertical marquee windows.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CharacterSet">
			<summary>Holds the character set for displaying characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CharacterSetList">
			<summary>Holds a string of character set numbers.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.Columns">
			<summary>Holds the number of columns for this window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CurrentWindow">
			<summary>Holds the current window to which text is displayed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CursorColumn">
			<summary>Holds the column in the current window to which the next displayed character will be output.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CursorRow">
			<summary>Holds the row in the current window to which the next displayed character will be output.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CursorType">
			<summary>Holds the cursor type for the current window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CursorUpdate">
			<summary>If true when characters are displayed using the <see cref="M:Microsoft.PointOfService.LineDisplay.DisplayText(System.String)"></see> and <see cref="M:Microsoft.PointOfService.LineDisplay.DisplayTextAt(System.Int32,System.Int32,System.String)"></see> methods, then the <see cref="P:Microsoft.PointOfService.LineDisplay.CursorRow"></see> and <see cref="P:Microsoft.PointOfService.LineDisplay.CursorColumn"></see> properties will be updated to point to the character beyond the last character output.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.CustomGlyphList">
			<summary>Holds an array of instances of the <see cref="T:Microsoft.PointOfService.RangeOfCharacters"></see> helper class, which represents the character code ranges that are available for defining glyphs.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.DeviceBrightness">
			<summary>Holds the device brightness value, expressed as a percentage between 0 and 100.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.DeviceColumns">
			<summary>Holds the number of columns on this device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.DeviceDescriptors">
			<summary>Holds the number of descriptors on this device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.DeviceRows">
			<summary>Holds the number of rows on this device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.DeviceWindows">
			<summary>Holds the maximum number of windows supported by the line display device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.GlyphHeight">
			<summary>Indicates the glyph height based on the number of pixels for a character cell.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.GlyphWidth">
			<summary>Indicates the glyph width based on the number of pixels for a character cell.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.InterCharacterWait">
			<summary>Holds the wait time, specified in milliseconds, between the display of each character with the <see cref="M:Microsoft.PointOfService.LineDisplay.DisplayText(System.String)"></see> and <see cref="M:Microsoft.PointOfService.LineDisplay.DisplayTextAt(System.Int32,System.Int32,System.String)"></see> methods.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.MapCharacterSet">
			<summary>If true when outputting data, the service object maps the characters transferred by the application to the character set selected in the <see cref="P:Microsoft.PointOfService.LineDisplay.CharacterSet"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.MarqueeFormat">
			<summary>Holds the marquee format for the current window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.MarqueeRepeatWait">
			<summary>Holds the wait time, specified in milliseconds, between scrolling the final character or row of the window into its viewport and restarting the marquee with the first or last character or row.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.MarqueeType">
			<summary>Holds the marquee type for the current window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.MarqueeUnitWait">
			<summary>Holds the wait time, specified in milliseconds, between marquee scrolling of each column or row in the window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.MaximumX">
			<summary>A value of 0 (zero) indicates that bitmaps are not supported.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.MaximumY">
			<summary>A value of zero indicates that bitmaps aren’t supported.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.Rows">
			<summary>Holds the number of rows for this window.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.ScreenMode">
			<summary>Holds the screen mode value of the device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.ScreenModeList">
			<summary>Holds the array of <see cref="T:Microsoft.PointOfService.DisplayScreenMode"></see> class instances that are supported by the device.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayScreenMode">
			<summary>An array of instances of <see cref="T:Microsoft.PointOfService.DisplayScreenMode"></see> is used to provide a list of supported screen modes for the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayProperties.ScreenModeList"></see> property.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.LineDisplayScreenMode.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
			<summary>Creates a new instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.LineDisplayScreenMode"></see> class with the specified number of columns, rows, glyph width, and glyph height.</summary>
			<param name="glyphHeight">The glyph height based on the number of pixels for a character cell.</param>
			<param name="glyphWidth">The glyph width based on the number of pixels for a character cell.</param>
			<param name="deviceColumns">The number of device columns.</param>
			<param name="deviceRows">The number of device rows.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayScreenMode.GlyphHeight">
			<summary>The glyph height based on the number of pixels for a character cell.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayScreenMode.GlyphWidth">
			<summary>The glyph width based on the number of pixels for a character cell.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayScreenMode.MaximumX">
			<summary>The maximum number of horizontal pixels supported by the device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.LineDisplayScreenMode.MaximumY">
			<summary>The maximum number of vertical pixels supported by the device.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.MarkFeedOperation">
			<summary>Used to capture information about the <see cref="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MarkFeed(Microsoft.PointOfService.PrinterMarkFeeds)"></see> method call.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MarkFeedOperation.Type">
			<summary>Contains the value of the type parameter from the <see cref="PosPrinter.MarkFeed"></see> method.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.MsrBase">
			<summary>Contains most of the functionality for support of MSR devices.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.#ctor">
			<summary>Creates a new instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.MsrBase"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.ClearInputProperties">
			<summary>Resets the values of properties altered by DataEvent or ErrorEvent. </summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.FailedRead(System.Byte[],System.Byte[],System.Byte[],System.Byte[],Microsoft.PointOfService.BaseServiceObjects.CardType,Microsoft.PointOfService.ErrorCode,System.Int32)">
			<summary>Called by the service object when an error occurs while the MSR device is attempting to read card data.</summary>
			<param name="errorCode">Contains the error reporting code, as defined by the <see cref="ErrorCode"></see> enumeration.</param>
			<param name="cardType">Contains the type of card swiped, as defined by the <see cref="CardType"></see> enumeration.</param>
			<param name="track1Data">Contains the data read for Track1.</param>
			<param name="extendedErrorCode">Contains one of the <see cref="ExtendedError~"></see> constants that describes the type of card read error.</param>
			<param name="track3Data">Contains the data read for Track3.</param>
			<param name="track2Data">Contains the data read for Track2.</param>
			<param name="track4Data">Contains the data read for Track4.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.GoodRead(System.Byte[],System.Byte[],System.Byte[],System.Byte[],Microsoft.PointOfService.BaseServiceObjects.CardType)">
			<summary>Called by the service object when the card swipe has been successful.</summary>
			<param name="cardType">Contains the type of card swiped, as defined by the <see cref="CardType"></see> enumeration.</param>
			<param name="track1Data">Contains the data read for Track1.</param>
			<param name="track3Data">Contains the data read for Track3.</param>
			<param name="track2Data">Contains the data read for Track2.</param>
			<param name="track4Data">Contains the data read for Track4.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Open">
			<summary>Initializes MSR properties <see cref="DecodeData"></see>, <see cref="ParseDecodeData"></see>, <see cref="TransmitSentinels"></see>, <see cref="ErrorReprtingType"></see>, and <see cref="MsrTracksToRead"></see>.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.ParseMsrFieldData(System.Byte[],System.Byte[],System.Byte[],System.Byte[],Microsoft.PointOfService.BaseServiceObjects.CardType)">
			<summary>Called to decode card data into the appropriate properties.</summary>
			<param name="cardType">Contains the type of card swiped, as defined by the <see cref="CardType"></see> enumeration.</param>
			<param name="track1Data">Contains the data read for Track1</param>
			<param name="track3Data">Contains the data read for Track3</param>
			<param name="track2Data">Contains the data read for Track2</param>
			<param name="track4Data">Contains the data read for Track4</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.ParseMsrTrackData(System.Byte[],System.Byte[],System.Byte[],System.Byte[],Microsoft.PointOfService.BaseServiceObjects.CardType)">
			<summary>Called to decode card data into the appropriate properties.</summary>
			<param name="cardType">Contains the type of card swiped, as defined by the <see cref="CardType"></see> enumeration.</param>
			<param name="track1Data">Contains the data read for Track1.</param>
			<param name="track3Data">Contains the data read for Track3.</param>
			<param name="track2Data">Contains the data read for Track2.</param>
			<param name="track4Data">Contains the data read for Track4.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.PreFireEvent(Microsoft.PointOfService.DataEventArgs)">
			<summary>Called prior to the delivery of a <see cref="DataEvent"></see> event to the application.</summary>
			<param name="posEvent">An instance of the <see cref="DataEventArgs"></see> class, posEvent contains the event that was queued to the application.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.PreFireEvent(Microsoft.PointOfService.DeviceErrorEventArgs)">
			<summary>Called prior to the delivery of a <see cref="DeviceErrorEvent"></see> event to the application.</summary>
			<param name="posEvent">An instance of the <see cref="DeviceErrorEventArgs"></see> class, posEvent contains the event that was queued to the application.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.SetMaxTrackEncodingLengths(System.Int32,System.Int32,System.Int32,System.Int32)">
			<summary>Sets the maximum encoding length for the specified Msr data tracks.</summary>
			<param name="track3MaxLength">An integer representing the maximum track length for data track 3.</param>
			<param name="track4MaxLength">An integer representing the maximum track length for data track 4.</param>
			<param name="track1MaxLength">An integer representing the maximum track length for data track 1.</param>
			<param name="track2MaxLength">An integer representing the maximum track length for data track 2.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.UnreadableCard(Microsoft.PointOfService.ErrorCode,System.Int32)">
			<summary>Called by the service object when the card data is unreadable.</summary>
			<param name="errorCode">Contains the error code, as defined by the <see cref="ErrorCode"></see> enumeration.</param>
			<param name="extendedErrorCode">Contains one of the <see cref="ExtendedError~"></see> constants that describes the type of read error.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrBase.WriteTracks(System.Byte[],System.Byte[],System.Byte[],System.Byte[],System.Int32)">
			<summary>Initiates the encoding of data to the MSR track(s) selected in the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.TracksToWrite"></see> property.</summary>
			<param name="track1Data">The first data track.</param>
			<param name="track3Data">The third data track.</param>
			<param name="timeout">The number of milliseconds before failing the method. If -1, the method waits as long as need before the card is swiped.</param>
			<param name="track2Data">The second data track.</param>
			<param name="track4Data">The fourth data track.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.AccountNumber">
			<summary>Holds the account number from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.CapIso">
			<summary>Indicates that the MSR supports ISO cards.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.CapJisOne">
			<summary>Indicates that the MSR device supports JIS Type-I cards.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.CapJisTwo">
			<summary>Indicates that the MSR device supports JIS Type-II cards.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.CapTransmitSentinels">
			<summary>Indicates that the MSR device can transmit start and end sentinels.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.DecodeData">
			<summary>If false, the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track1Data"></see>, <see cref="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track2Data"></see>, <see cref="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track3Data"></see>, and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track4Data"></see> properties contain the original encoded bit sequence, known as “raw format.”</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.ErrorReportingType">
			<summary>Holds the type of error reporting the application wants to receive. Possible values are defined by the <see cref="T:Microsoft.PointOfService.MsrErrorReporting"></see> enumeration. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.ExpirationDate">
			<summary>Holds the expiration date from the most recently swiped card, as four ASCII decimal characters in the form YYMM.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.FirstName">
			<summary>Holds the first name from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.MiddleInitial">
			<summary>Holds the middle initial from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.ParseDecodeData">
			<summary>If true, the decoded data contained within the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track1Data"></see> and <see cref="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track2Data"></see> properties is further separated into fields for access via various other properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Properties">
			<summary>Contains getters and setters for common and Msr specific properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.ServiceCode">
			<summary>Holds the service code from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Suffix">
			<summary>Holds the suffix from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Surname">
			<summary>Holds the surname from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Title">
			<summary>Holds the title from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track1Data">
			<summary>Holds either the Track 1 data from the most recently swiped card or an empty array. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track1DiscretionaryData">
			<summary>Holds the Track 1 discretionary data from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track2Data">
			<summary>Holds either the Track 2 data from the most recently swiped card or an empty array.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track2DiscretionaryData">
			<summary>The Track 2 discretionary data from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track3Data">
			<summary>Holds either the Track 3 data from the most recently swiped card or an empty array.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.Track4Data">
			<summary>Holds the Track 4 data (JIS-II) from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.TracksToRead">
			<summary>Indicates the track data that the application wishes to place into the <see cref="Track1Data"></see>, <see cref="Track2Data"></see>, <see cref="Track3Data"></see> and <see cref="Track4Data"></see> properties following a card swipe.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrBase.TransmitSentinels">
			<summary>Indicates that the Track1Data, Track2Data, Track3Data, and Track4Data properties contain start sentinel and end sentinel values. </summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData">
			<summary>An instance of a <see cref="MsrFieldData"></see> class is returned by the <see cref="ParseMsrFieldData"></see> protected method of the <see cref="MsrBase"></see> class. The string fields and discretionary data are decoded, and then stored in their respective properties.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.#ctor">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Byte[],System.Byte[])">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData"></see> class with the specified parameters.</summary>
			<param name="accountNumber">The account number from the most recently swiped card.</param>
			<param name="middleInitial">The middle initial from the most recently swiped card.</param>
			<param name="track1DiscretionaryData">The Track 1 discretionary data from the most recently swiped card.</param>
			<param name="firstName">The first name from the most recently swiped card.</param>
			<param name="suffix">The suffix from the most recently swiped card.</param>
			<param name="surname">The surname from the most recently swiped card.</param>
			<param name="serviceCode">The service code from the most recently swiped card.</param>
			<param name="expirationDate">The expiration date from the most recently swiped card, as four ASCII decimal characters in the form YYMM.</param>
			<param name="track2DiscretionaryData">The Track 2 discretionary data from the most recently swiped card.</param>
			<param name="title">The title from the most recently swiped card.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData"></see> class based on the parameters specified.</summary>
			<param name="expirationDate">The expiration date in the most recently swiped card.</param>
			<param name="hairColor">The color of the hair in the most recently swiped card.</param>
			<param name="endorsements">The endorsements in the most recently swiped card.</param>
			<param name="birthDate">The birth date in the most recently swiped card.</param>
			<param name="surname">The surname in the most recently swiped card.</param>
			<param name="eyeColor">The color of the eye in the most recently swiped card.</param>
			<param name="licenseNumber">The license number in the most recently swiped card.</param>
			<param name="restrictions">The restrictions in the most recently swiped card.</param>
			<param name="firstName">The first name in the most recently swiped card.</param>
			<param name="city">The city in the most recently swiped card.</param>
			<param name="_class">The class in the most recently swiped card.</param>
			<param name="address">The address in the most recently swiped card.</param>
			<param name="weight">The weight as stated in the most recently swiped card.</param>
			<param name="postalCode">The postal code in the most recently swiped card.</param>
			<param name="gender">The gender in the most recently swiped card.</param>
			<param name="suffix">The suffix in the most recently swiped card.</param>
			<param name="height">The height in the most recently swiped card.</param>
			<param name="state">The name of the state in the most recently swiped card.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.AccountNumber">
			<summary>Gets or sets the account number from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Address">
			<summary>Gets or sets the address in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.BirthDate">
			<summary>Gets or sets the date of birth in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.City">
			<summary>Gets or sets the name of the city in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Class">
			<summary>Gets or sets the class in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Endorsements">
			<summary>Gets or sets the endorsements in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.ExpirationDate">
			<summary>Gets or sets the expiration date from the most recently swiped card, as four ASCII decimal characters in the form YYMM. For example, February 1998 is "9802" and August 2018 is "1808".</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.EyeColor">
			<summary>Gets or sets the eye color field in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.FirstName">
			<summary>Gets or sets the first name from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Gender">
			<summary>Gets or sets the gender field in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.HairColor">
			<summary>Gets or sets the hair color field in the most recently swiped card.</summary>
			<returns>Return a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Height">
			<summary>Gets or sets the height field in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.LicenseNumber">
			<summary>Gets or sets the license number in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.MiddleInitial">
			<summary>Gets or sets the middle initial from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.PostalCode">
			<summary>Gets or sets the postal code in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Restrictions">
			<summary>Gets or sets the restrictions field in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.ServiceCode">
			<summary>Gets or sets the service code from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.State">
			<summary>Gets or sets the state field in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Suffix">
			<summary>Gets or sets the suffix from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Surname">
			<summary>Gets or sets the surname from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Title">
			<summary>Gets or sets the title from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Track1DiscretionaryData">
			<summary>Gets or sets the Track 1 discretionary data from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Track2DiscretionaryData">
			<summary>Gets or sets the Track 2 discretionary data from the most recently swiped card.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrFieldData.Weight">
			<summary>Gets or sets the weight field in the most recently swiped card.</summary>
			<returns>Returns a string.</returns>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.MsrProperties">
			<summary>A helper class created by POS for .NET to simplify the retrieval and setting of property values for MSR devices.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrProperties.CapIso">
			<summary>Indicates that the MSR supports ISO cards.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrProperties.CapJisOne">
			<summary>Indicates that the MSR device supports JIS Type-I cards.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrProperties.CapTransmitSentinels">
			<summary>Indicates that the MSR device can transmit start and end sentinels.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData">
			<summary>An instance of <see cref="MsrTrackData"></see> is returned by the <see cref="ParseMsrTrackData"></see> protected method of the <see cref="MsrBase"></see> class. Tracks 1 through 4 are decoded and stored in each of the four, discrete Track properties.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData.#ctor">
			<summary>Creates a new instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData.#ctor(System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
			<summary>Creates a new instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData"></see> class with the specified MSR track data.</summary>
			<param name="track3Data">Data from the third MSR track.</param>
			<param name="track1Data">Data from the first MSR track.</param>
			<param name="track2Data">Data from the second MSR track.</param>
			<param name="track4Data">Data from the fourth MSR track.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData.Track1Data">
			<summary>Holds decoded data from the first MSR track.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData.Track2Data">
			<summary>Holds decoded data from the second MSR track.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData.Track3Data">
			<summary>Holds decoded data from the third MSR track.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.MsrTrackData.Track4Data">
			<summary>Holds decoded data from the fourth MSR track.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PageModePrintCollection">
			<summary>Prints a collection of <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PageModePrintOperation"></see>.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PageModePrintOperation">
			<summary>Indicates a single page mode print operation.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PinPadBase">
			<summary>Implements all properties and methods for supporting PIN pad devices.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.#ctor">
			<summary>Creates an instance of the <see cref="PinPadBase"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.BeginEftTransaction(Microsoft.PointOfService.PinPadSystem,System.Int32)">
			<summary>Informs the PIN Pad’s service object that an EFT Transaction is starting.</summary>
			<param name="transactionHost">Identifies the EFT Transaction Host to be used for this transaction.</param>
			<param name="pinpadSystem">Name of the desired PIN Pad Management System.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.BeginEftTransactionImpl(Microsoft.PointOfService.PinPadSystem,System.Int32)">
			<summary>Informs the PIN Pad’s service object that an EFT Transaction is starting.</summary>
			<param name="transactionHost">Identifies the EFT Transaction Host to be used for this transaction.</param>
			<param name="pinpadSystem">Name of the desired PIN Pad Management System.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.ClearInputProperties">
			<summary>Resets the values of properties altered by <see cref="E:Microsoft.PointOfService.PinPad.DataEvent"></see> or <see cref="E:Microsoft.PointOfService.PinPad.ErrorEvent"></see>.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.ComputeMac(System.String)">
			<summary>Tells the service object to compute a MAC value and return it to the application. </summary>
			<param name="inMsg">Contains the message that the application intends to send to the EFT Transaction Host.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.ComputeMacImpl(System.String)">
			<summary>The <see cref="PinPadBase"></see> class calls <see cref="ComputeMacImpl"></see> from its <see cref="ComputeMac"></see> method.</summary>
			<param name="inMsg">Contains the message that the application intends to send to the EFT Transaction Host.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.EnablePinEntry">
			<summary>Starts PIN entry at the PIN pad device.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.EnablePinEntryImpl">
			<summary>The <see cref="PinPadBase"></see> class calls <see cref="EnablePinEntryImpl"></see> from its <see cref="EnablePinEntry"></see> method.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.EndEftTransaction(Microsoft.PointOfService.EftTransactionCompletion)">
			<summary>Takes the PIN pad out of EFT Transaction Mode.</summary>
			<param name="completionCode">The transaction completion code.Valid values for the completionCode are defined by the <see cref="EftTransactionCompletion"></see> enumeration.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.EndEftTransactionImpl(Microsoft.PointOfService.EftTransactionCompletion)">
			<summary>The <see cref="PinPadBase"></see> class calls <see cref="EndEftTransactionImpl"></see> from its <see cref="EndEftTransaction"></see> method.</summary>
			<param name="completionCode">The transaction completion code.Valid values for the completionCode are defined by the <see cref="EftTransactionCompletion"></see> enumeration.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.ExitPinEntryMode(Microsoft.PointOfService.PinEntryStatus,System.String,System.String)">
			<summary>Called by the service object to exit PIN entry mode.</summary>
			<param name="encryptedPin">If status is set to Success, encryptedPin should contain the encrypted data for the PIN that has been entered; otherwise, it should be set to null.</param>
			<param name="status">Contains the current status of PIN entry. Possible values are defined by the <see cref="PinEntryStatus"></see> enumerator.</param>
			<param name="additionalSecurityInformation">If status is set to Success, additionalSecurityInformation should contain any pertinent additional security information; otherwise, it should be set to null.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.Open">
			<summary>Opens a device for later input/output processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.PreFireEvent(Microsoft.PointOfService.DataEventArgs)">
			<summary>Called prior to the delivery of a <see cref="DataEvent"></see> event to the application.</summary>
			<param name="posEvent">An instance of the <see cref="DataEventArgs"></see> class, posEvent contains the event that was queued to the application.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.UpdateKey(System.Int32,System.String)">
			<summary>Provides a new encryption key to the PIN pad service object for those PIN Pad Management Systems in which new key values are sent to the terminal as a field in standard messages from the EFT Transaction Host.</summary>
			<param name="keyNumber">Contains a key number.</param>
			<param name="key">Contains a Hex-ASCII value for a new key.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.UpdateKeyImpl(System.Int32,System.String)">
			<summary>The <see cref="PinPadBase"></see> class calls <see cref="UpdateKeyImpl"></see> from its <see cref="UpdateKey"></see> method.</summary>
			<param name="keyNumber">Contains a key number.</param>
			<param name="key">Contains a Hex-ASCII value for a new key.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.VerifyMac(System.String)">
			<summary>Tells the service object to verify the MAC value in a message received from an EFT Transaction Host.</summary>
			<param name="message">The message parameter contains a message received from an EFT Transaction Host.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.VerifyMacImpl(System.String)">
			<summary>The <see cref="PinPadBase"></see> class calls <see cref="VerifyMacImpl"></see> from its <see cref="VerifyMac"></see> method.</summary>
			<param name="message">The message parameter contains a message received from an EFT Transaction Host.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.AccountNumber">
			<summary>Holds the user’s account number for the current EFT transaction.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.AdditionalSecurityInformation">
			<summary>Holds security/encryption information, formatted as a Hex-ASCII string, after a successful PIN entry operation in which the <see cref="DataEvent"></see> event is queued to the application with a <see cref="DataEventArgs.Status"></see> of Success.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.Amount">
			<summary>Holds the amount of the current EFT transaction.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.AvailableLanguagesList">
			<summary>Holds an array of the languages supported by the pre-defined prompts in the PIN pad device, as defined by the <see cref="CultureInfo"></see> .NET Framework Class.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.AvailablePromptsList">
			<summary>Holds a comma-separated string of supported values for the <see cref="Prompt"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.CapDisplay">
			<summary>Indicates the operations that the application can perform on the PIN pad display.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.CapKeyboard">
			<summary>If set to true, the application can use the PIN pad keyboard to obtain input.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.CapLanguage">
			<summary>Indicates the language of pre-defined messages for the PIN pad device (for example, English, French, Arabic, and so on).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.CapMacCalculation">
			<summary>If set to true, the PIN pad supports MAC calculation.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.CapTone">
			<summary>If set to true, the PIN pad has a tone indicator, which is implemented as a <see cref="ToneIndicator"></see> class object.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.EncryptedPin">
			<summary>Holds the value of the encrypted PIN after the <see cref="DataEvent"></see> event notifies the application that a PIN entry operation has been completed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.MaximumPinLength">
			<summary>Holds the maximum acceptable number of digits to be entered for the PIN.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.MerchantId">
			<summary>Holds the Merchant ID, as it is known to the EFT Transaction Host.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.MinimumPinLength">
			<summary>Holds the minimum acceptable number of digits to be entered for a PIN.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.PinEntryEnabled">
			<summary>Indicates whether the PinPad is ready to accept PIN entry.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.Prompt">
			<summary>Holds a pre-defined message to be displayed on the PIN Pad.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.PromptLanguage">
			<summary>Specifies the language in which the message specified by the <see cref="Prompt"></see> property will be displayed, as defined by the <see cref="CultureInfo"></see> .NET Framework class. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.Properties">
			<summary>Contains getters and setters for <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PinPadBase"></see> and <see cref="Common"></see> properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.TerminalId">
			<summary>Holds the Terminal ID, as it is known to the EFT Transaction Host.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.Track1Data">
			<summary>Holds either the decoded Track 1 data from the previous card swipe or an empty string.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.Track2Data">
			<summary>Holds either the decoded Track 2 data from the previous card swipe or an empty string.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.Track3Data">
			<summary>Holds either the decoded Track 3 data from the previous card swipe or an empty string.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.Track4Data">
			<summary>Holds either the decoded Track 4 data from the previous card swipe or an empty string.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadBase.TransactionType">
			<summary>Holds the current EFT transaction type.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties">
			<summary>A helper class created by POS for .NET to simplify the retrieval and setting of property values for PIN pad devices.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.AccountNumber">
			<summary>Holds the user’s account number for the current EFT transaction.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.AdditionalSecurityInformation">
			<summary>Holds security/encryption information, formatted as a Hex-ASCII string, after a successful PIN entry operation in which the <see cref="DataEvent"></see> event is queued to the application with a <see cref="DataEventArgs.Status"></see> of Success.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.Amount">
			<summary>Holds the amount of the current EFT transaction.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.AvailableLanguagesList">
			<summary>Holds an array of the languages supported by the pre-defined prompts in the PIN pad device, as defined by the <see cref="CultureInfo"></see> .NET Framework Class.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.AvailablePromptsList">
			<summary>Holds a comma-separated string of supported values for the <see cref="Prompt"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.CapDisplay">
			<summary>Indicates the operations that the application can perform on the PIN pad display.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.CapKeyboard">
			<summary>If set to true, the application can use the PIN pad keyboard to obtain input.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.CapLanguage">
			<summary>Indicates the language of pre-defined messages for the PIN pad device (for example, English, French, Arabic, and so on).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.CapMacCalculation">
			<summary>If set to true, the PIN pad supports MAC calculation.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.CapTone">
			<summary>If set to true, the PIN pad has a tone indicator, which is implemented as a <see cref="ToneIndicator"></see> class object.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.EncryptedPin">
			<summary>Holds the value of the encrypted PIN after the <see cref="DataEvent"></see> event notifies the application that a PIN entry operation has been completed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.MaximumPinLength">
			<summary>Holds the maximum acceptable number of digits to be entered for the PIN.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.MerchantId">
			<summary>Holds the Merchant ID, as it is known to the EFT Transaction Host.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.MinimumPinLength">
			<summary>Holds the minimum acceptable number of digits to be entered for a PIN.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.PinEntryEnabled">
			<summary>Indicates whether the PinPad is ready to accept PIN entry.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.Prompt">
			<summary>Holds a pre-defined message to be displayed on the PIN Pad.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.PromptLanguage">
			<summary>Specifies the language in which the message specified by the <see cref="Prompt"></see> property will be displayed, as defined by the <see cref="CultureInfo"></see> .NET Framework class. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.TerminalId">
			<summary>Holds the Terminal ID, as it is known to the EFT Transaction Host.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.Track1Data">
			<summary>Holds either the decoded Track 1 data from the previous card swipe or an empty string.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.Track2Data">
			<summary>Holds either the decoded Track 2 data from the previous card swipe or an empty string.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.Track3Data">
			<summary>Holds either the decoded Track 3 data from the previous card swipe or an empty string.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.Track4Data">
			<summary>Holds either the decoded Track 4 data from the previous card swipe or an empty string.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PinPadProperties.TransactionType">
			<summary>Holds the current EFT transaction type.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase">
			<summary>Contains compete device functionality implemented by POS for .NET for support of keyboard devices.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.#ctor">
			<summary>Creates an instance of the <see cref="PosKeyboardBase"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.KeyDown(System.Int32)">
			<summary>Holds the type of the last keyboard event.</summary>
			<param name="keyData">The keyData parameter contains the value of the key pressed, defined as a logical key code in the upper 16 bits and a scan code in the lower 16 bits, where the values need not match a standard PC keyboard’s values.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.KeyUp(System.Int32)">
			<summary>If true, the keyboard device is able to generate both key-down and key-up input data, based on the value of the <see cref="EventTypes"></see> property.</summary>
			<param name="keyData">The keyData parameter contains the value of the key pressed, defined as a logical key code in the upper 16 bits and a scan code in the lower 16 bits, where the values need not match a standard PC keyboard's values.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.Open">
			<summary>Opens a device for later input/output processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.PreFireEvent(Microsoft.PointOfService.DataEventArgs)">
			<summary>Called prior to the delivery of a <see cref="DataEvent"></see> event to the application.</summary>
			<param name="posEvent">An instance of the <see cref="DataEventArgs"></see> class, posEvent contains the event the service object queued to the application.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.CapKeyUp">
			<summary>If true, the keyboard device is able to generate both key-down and key-up input data, based on the value of the <see cref="EventTypes"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.EventTypes">
			<summary>Holds the type of events that the application wants to receive.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.PosKeyData">
			<summary>Holds the value of the key from the last <see cref="DataEvent"></see>.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.PosKeyEventType">
			<summary>Holds the value of the key from the last <see cref="DataEvent"></see>.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardBase.Properties">
			<summary>Contains getters and setters for <see cref="PosKeyboard"></see> and <see cref="Common"></see> properties.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardProperties">
			<summary>A helper class created by POS for .NET to simplify the retrieval and setting of property values for keyboard devices.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardProperties.CapKeyUp">
			<summary>If true, the keyboard device is able to generate both key-down and key-up input data, based on the value of the <see cref="EventTypes"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardProperties.EventTypes">
			<summary>Holds the type of events that the application wants to receive.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardProperties.PosKeyData">
			<summary>Holds the value of the key from the last <see cref="DataEvent"></see>.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosKeyboardProperties.PosKeyEventType">
			<summary>Holds the type of the last keyboard event.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase">
			<summary><see cref="PosPrinterBase"></see> class derives from <see cref="PosPrinterBasic"></see> class and contains more complete device functionality implemented by POS for .NET for support of POS printer devices.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.#ctor">
			<summary>Creates a new instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase"></see> class.</summary>
		</member>
		<member name="E:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CancelPrintEvent">
			<summary>Raised when the current print operation should be cancelled.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.BeginInsertion(System.Int32)">
			<summary>Initiates form insertion processing. <see cref="BeginInsertion"></see> is paired with the <see cref="EndInsertion"></see> method to control form insertion.</summary>
			<param name="timeout">The number of milliseconds before failing the method.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.BeginInsertionImpl(System.Int32)">
			<summary>Implemented by service objects to process BeginInsertion requests.</summary>
			<param name="timeout">The number of milliseconds before failing the method.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.BeginRemoval(System.Int32)">
			<summary>Initiates form removal processing.</summary>
			<param name="timeout">The number of milliseconds before failing the method.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.BeginRemovalImpl(System.Int32)">
			<summary>Implemented by service objects to process BeginRemoval requests.</summary>
			<param name="timeout">The number of milliseconds before failing the method.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ChangePrintSide(Microsoft.PointOfService.PrinterSide)">
			<summary>Selects the side of the document it wants to print on.</summary>
			<param name="side">The side on which to print. </param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ChangePrintSideImpl(Microsoft.PointOfService.PrinterSide)">
			<summary>Method implemented by service objects to process <see cref="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ChangePrintSide(Microsoft.PointOfService.PrinterSide)"></see> requests.</summary>
			<param name="side">The side on which to print.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ClearOutput">
			<summary>Clears all device output that has been buffered. Also, when possible, to halt outputs that are in progress.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ClearPrintArea">
			<summary>Clears the area defined by the <see cref="PageModePrintArea"></see> property.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CutPaper(System.Int32)">
			<summary>Cuts the receipt paper.</summary>
			<param name="percentage">The percentage of paper to cut.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CutPaperImpl(System.Int32)">
			<summary>Method implemented by the service object to process CutPaper requests.</summary>
			<param name="percentage">The percentage of paper to cut.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.EndInsertion">
			<summary>Ends form-insertion processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.EndInsertionImpl">
			<summary>Method implemented by the service object to process <see cref="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.EndInsertion"></see> requests.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.EndRemoval">
			<summary>Ends form-removal processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.EndRemovalImpl">
			<summary>Method implemented by service objects to process EndRemoval requests.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MarkFeed(Microsoft.PointOfService.PrinterMarkFeeds)">
			<summary>Holds the type of mark-sensed paper handling available.</summary>
			<param name="type">Indicates the type of mark-sensed paper handling. </param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MarkFeedImpl(Microsoft.PointOfService.PrinterMarkFeeds)">
			<summary>Method implemented by the service object to process MarkFeed requests.</summary>
			<param name="type">The type of mark-sensed paper handling.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.Open">
			<summary>Opens a device for later input/output processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModePrint(Microsoft.PointOfService.PageModePrintControl)">
			<summary>Enters or exits Page Mode for the station specified in the <see cref="PageModeStation"></see> property.</summary>
			<param name="control">The page mode control.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModePrintImpl(Microsoft.PointOfService.BaseServiceObjects.PageModePrintCollection)">
			<summary>Method implemented by service objects to process PageModePrint requests.</summary>
			<param name="collection">The collection of PageModePrint requests.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PreFireEvent(Microsoft.PointOfService.DeviceErrorEventArgs)">
			<summary>Called before the delivery of an ErrorEvent event.</summary>
			<param name="posEvent">Class containing error information that will be sent to the application when the <see cref="T:Microsoft.PointOfService.DeviceErrorEventHandler"></see> is raised.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PreQueuePrintData(Microsoft.PointOfService.BaseServiceObjects.PrintOperationCollection)">
			<summary>This method is called before print requests are added to the print queue.</summary>
			<param name="printCollection">A collection of <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PrintOperation"></see> objects to be queued.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintBarCode(Microsoft.PointOfService.PrinterStation,System.String,Microsoft.PointOfService.BarCodeSymbology,System.Int32,System.Int32,System.Int32,Microsoft.PointOfService.BarCodeTextPosition)">
			<summary>Prints a bar code on the specified printer station.</summary>
			<param name="alignment">Placement of the bar code. For possible values, see Remarks below.</param>
			<param name="symbology">Bar code symbol type to use. Possible values are defined by the BarCodeSymbology enumeration.</param>
			<param name="textPosition">Placement of the readable character string. Possible values are defined by the BarCodeTextPosition enumeration.</param>
			<param name="data">The bar code data to be printed.</param>
			<param name="width">Bar code width.</param>
			<param name="station">The printer station to be used. Possible values are defined by the PrinterStation enumeration. Valid values are PrinterStation.Receipt or PrinterStation.Slip.</param>
			<param name="height">Bar code height. Expressed in the unit of measure indicated by the MapMode property</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintBarCodeImpl(Microsoft.PointOfService.PrinterStation,System.String,Microsoft.PointOfService.BarCodeSymbology,System.Int32,System.Int32,System.Int32,Microsoft.PointOfService.BarCodeTextPosition)">
			<summary>Method implemented by the service object to process PrintBarCode requests.</summary>
			<param name="alignment">Placement of the bar code..</param>
			<param name="symbology">Bar code symbol type to use. Possible values are defined by the <see cref="T:Microsoft.PointOfService.BarCodeSymbology"></see> enumeration.</param>
			<param name="textPosition">Placement of the readable character string. Possible values are defined by the <see cref="T:Microsoft.PointOfService.BarCodeTextPosition"></see> enumeration.</param>
			<param name="data">The bar code data to be printed.</param>
			<param name="width">Bar code width.</param>
			<param name="station">The printer station to be used. Possible values are defined by the <see cref="T:Microsoft.PointOfService.PrinterStation"></see> enumeration. Valid values are PrinterStation.Receipt or PrinterStation.Slip.</param>
			<param name="height">Bar code height. Expressed in the unit of measure indicated by the <see cref="T:Microsoft.PointOfService.MapMode"></see> property</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintBarCodeImpl(Microsoft.PointOfService.PrinterStation,System.String,Microsoft.PointOfService.BarCodeSymbology,System.Int32,System.Int32,System.Int32,Microsoft.PointOfService.BarCodeTextPosition,Microsoft.PointOfService.BaseServiceObjects.PrinterState)">
			<summary>Implemented by service objects to process PrintBarCode requests.</summary>
			<param name="s tate">The state of the printer at the time of the request.</param>
			<param name="alignment">Placement of the bar code. </param>
			<param name="symbology">Bar code symbol type to use.</param>
			<param name="textPosition">Placement of the readable character string. Possible values are defined by the <see cref="BarCodeTextPosition"></see> enumeration.</param>
			<param name="w idth">Bar code width. Expressed in the unit of measure indicated by the <see cref="MapMode"></see> property.</param>
			<param name="h eight">Bar code height. Expressed in the unit of measure indicated by the <see cref="MapMode"></see> property.</param>
			<param name="s tation">The printer station to be used. Possible values are defined by the PrinterStation enumeration.</param>
			<param name="d ata">The character string to be printed as a bar code.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintBitmap(Microsoft.PointOfService.PrinterStation,System.String,System.Int32,System.Int32)">
			<summary>Prints a bitmap on the specified printer station. </summary>
			<param name="alignment">Placement of the bitmap.</param>
			<param name="width">Width of the bitmap.</param>
			<param name="station">The printer station to be used. Possible values are defined by the <see cref="T:Microsoft.PointOfService.PrinterStation"></see> enumeration. Valid values are PrinterStation.Receipt or PrinterStation.Slip.</param>
			<param name="fileName">Name of Windows bitmap file. The file must be in uncompressed format.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintBitmapImpl(Microsoft.PointOfService.PrinterStation,System.String,System.Int32,System.Int32)">
			<summary>Method implemented by service objects to process PrintBitmap requests.</summary>
			<param name="alignment">Placement of the bitmap.</param>
			<param name="width">Width of the bitmap.</param>
			<param name="station">The printer station to be used. Possible values are defined by the <see cref="T:Microsoft.PointOfService.PrinterStation"></see> enumeration. Valid values are PrinterStation.Receipt or PrinterStation.Slip.</param>
			<param name="fileName">Name of Windows bitmap file. The file must be in uncompressed format.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintBitmapImpl(Microsoft.PointOfService.PrinterStation,System.String,System.Int32,System.Int32,Microsoft.PointOfService.BaseServiceObjects.PrinterState)">
			<summary>Method implemented by service objects to process PrintBitmap requests.</summary>
			<param name="alignment">Placement of the bitmap. For possible values, see Remarks below.</param>
			<param name="width">Printed width of the bitmap to be performed. See Remarks below.</param>
			<param name="station">The printer station to be used. Possible values are defined by the <see cref="PrinterStation"></see> enumeration. Valid values are <see cref="PrinterStation.Receipt"></see> or <see cref="PrinterStation.Slip"></see>.</param>
			<param name="fileName">Name of Windows bitmap file. The file must be in uncompressed format.</param>
			<param name="state">The state of the printer at the time of the call to PrintBitmap.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintImmediate(Microsoft.PointOfService.PrinterStation,System.String)">
			<summary>Immediately prints the string specified for data on the printer station specified by station. </summary>
			<param name="data">The data to be written to the printer.</param>
			<param name="station">The <see cref="T:Microsoft.PointOfService.PrinterStation"></see> to which the data is to be written.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintImmediateImpl(Microsoft.PointOfService.PrinterStation,System.String)">
			<summary>Method implemented by service objects to process PrintImmediate requests.</summary>
			<param name="data">The data to be written to the printer.</param>
			<param name="station">The <see cref="T:Microsoft.PointOfService.PrinterStation"></see> to which the data is to be written.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintImmediateImpl(Microsoft.PointOfService.PrinterStation,System.String,Microsoft.PointOfService.BaseServiceObjects.PrinterState)">
			<summary>This method is called from PrintBitmap and can process either asynchronous or synchronous events without specialized code.</summary>
			<param name="data">The characters to be printed. May consist of printable characters, escape sequences, carriage returns (13 decimal), and new line / line feed (10 decimal).</param>
			<param name="station">The printer station to be used. Possible values are defined by the <see cref="PrinterStation"></see> enumeration. Valid values are: <see cref="PrinterStation.Journal"></see>, <see cref="PrinterStation.Receipt"></see>, or <see cref="PrinterStation.Slip"></see>.</param>
			<param name="state">The state of the printer at the time of the original call to PrintImmediate.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintMemoryBitmap(Microsoft.PointOfService.PrinterStation,System.Drawing.Bitmap,System.Int32,System.Int32)">
			<summary>Prints a memory-stored bitmap on the specified printer station.</summary>
			<param name="alignment">Placement of the bitmap. Valid alignments include left, right, center, and other values that express a distance from the left-most print column to the start of the bitmap. These values are expressed in the unit of measure given by <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MapMode"></see>.</param>
			<param name="data">Memory byte array representation of the bitmap.</param>
			<param name="width">Printed width of the bitmap to be performed. Valid widths include asIs and other bitmap width values expressed in the unit of measure given by <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MapMode"></see>.</param>
			<param name="station">The printer station to be used. May be eitherreceipt or slip.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintMemoryBitmapImpl(Microsoft.PointOfService.PrinterStation,System.Drawing.Bitmap,System.Int32,System.Int32,Microsoft.PointOfService.BaseServiceObjects.PrinterState)">
			<summary>Method implemented by service objects to process PrintMemoryBitmap requests.</summary>
			<param name="alignment">The placement of the bitmap.</param>
			<param name="bitmap">The bitmap to be printed</param>
			<param name="width">The width of the bitmap.</param>
			<param name="station">The printer station to be used. Can be either PrinterStation.Slip or PrinterStation.Receipt.</param>
			<param name="state">Contains printer state as it was when PrintMemoryBitmap was called.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintNormal(Microsoft.PointOfService.PrinterStation,System.String)">
			<summary>Prints the string specified in data on the printer station specified in station.</summary>
			<param name="data">The characters to print, consisting mostly of printable characters.</param>
			<param name="station">The <see cref="T:Microsoft.PointOfService.PrinterStation"></see> to use. May be Receipt , Journal, JournalReceipt, or Slip.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintNormalImpl(Microsoft.PointOfService.PrinterStation,Microsoft.PointOfService.BaseServiceObjects.PrinterState,System.String)">
			<summary>Method implemented by service objects to process PrintNormal requests.</summary>
			<param name="printerState">The current state of the printer object at the time of the call.</param>
			<param name="data">The data to be printed.</param>
			<param name="station">The <see cref="T:Microsoft.PointOfService.PrinterStation"></see> to be used.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintTwoNormal(Microsoft.PointOfService.PrinterStation,System.String,System.String)">
			<summary>Prints two strings on two print stations simultaneously. When supported, this may give increased print performance.</summary>
			<param name="stations">The printer stations to be used. Possible values are defined by the PrinterStation enumeration. Valid values are: PrinterStation.TwoReceiptJournal, PrinterStation.TwoSlipJournal, or PrinterStation.TwoSlipReceipt.</param>
			<param name="data2">The characters to be printed on the second station. (Restrictions are the same as data1.) If this string is the empty string (“”), then print the same data as data1. On some printers, using this format may give additional increased print performance.</param>
			<param name="data1">The characters to be printed on the first station. May consist of printable characters and escape sequences as listed in the <see cref="T:Microsoft.PointOfService.PosPrinter"></see> topic. The characters must all fit on one printed line, so that the printer may attempt to print on both stations simultaneously.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintTwoNormalImpl(Microsoft.PointOfService.PrinterStation,Microsoft.PointOfService.BaseServiceObjects.PrinterState,System.String,System.String)">
			<summary>Method implemented by service objects to process PrintTwoNormal requests.</summary>
			<param name="printerState">The state of the printer at the time the time of the original call.</param>
			<param name="stations">The printer stations to be used. Possible values are defined by the PrinterStation enumeration. Valid values are: PrinterStation.TwoReceiptJournal, PrinterStation.TwoSlipJournal, or PrinterStation.TwoSlipReceipt.</param>
			<param name="data2">The characters to be printed on the second station. (Restrictions are the same as data1.) If this string is the empty string (“”), then print the same data as data1. On some printers, using this format may give additional increased print performance.</param>
			<param name="data1">The characters to be printed on the first station. May consist of printable characters and escape sequences as listed in the <see cref="T:Microsoft.PointOfService.PosPrinter"></see> topic. The characters must all fit on one printed line, so that the printer may attempt to print on both stations simultaneously.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RotatePrint(Microsoft.PointOfService.PrinterStation,Microsoft.PointOfService.PrintRotation)">
			<summary>Enters or exits rotated print mode.</summary>
			<param name="rotation">The type of rotation requested.</param>
			<param name="station">The PrinterStation </param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RotatePrintImpl(Microsoft.PointOfService.BaseServiceObjects.RotatePrintCollection)">
			<summary>Method implemented by service objects to process RotatePrint requests.</summary>
			<param name="collection">The collection of rotate print requests.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SetBitmap(System.Int32,Microsoft.PointOfService.PrinterStation,System.String,System.Int32,System.Int32)">
			<summary>Saves information about a bitmap for later printing.</summary>
			<param name="alignment">Placement of the bitmap.</param>
			<param name="width">Width of the bitmap.</param>
			<param name="station">The printer station to be used. Possible values are defined by the <see cref="T:Microsoft.PointOfService.PrinterStation"></see> enumeration. Valid values are: PrinterStation.Receipt or PrinterStation.Slip.</param>
			<param name="fileName">Name of Windows bitmap file. The file must be in uncompressed format. If set to an empty string (“”), the bitmap is unset.</param>
			<param name="bitmapNumber">The number to be assigned to this bitmap. Two bitmaps, numbered 1 and 2, may be set.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SetBitmapImpl(System.Int32,Microsoft.PointOfService.PrinterStation,System.String,System.Int32,System.Int32)">
			<summary>Method implemented on service objects to process SetBitmap requests.</summary>
			<param name="alignment">Placement of the bitmap.</param>
			<param name="width">Width of the bitmap.</param>
			<param name="station">The printer station to be used. Possible values are defined by the <see cref="T:Microsoft.PointOfService.PrinterStation"></see> enumeration. Valid values are: PrinterStation.Receipt or PrinterStation.Slip.</param>
			<param name="fileName">Name of Windows bitmap file. The file must be in uncompressed format. If set to an empty string (“”), the bitmap is unset.</param>
			<param name="bitmapNumber">The number to be assigned to this bitmap. Two bitmaps, numbered 1 and 2, may be set.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SetLogo(Microsoft.PointOfService.PrinterLogoLocation,System.String)">
			<summary>Saves a data string as the top or bottom logo.</summary>
			<param name="data">Logo data.</param>
			<param name="location">Where the logo is to be set. Possible values are defined by the <see cref="T:Microsoft.PointOfService.PrinterLogoLocation"></see> enumeration as PrinterLogoLocation.Top or PrinterLogoLocation.Bottom.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.TransactionPrint(Microsoft.PointOfService.PrinterStation,Microsoft.PointOfService.PrinterTransactionControl)">
			<summary>Enters or exits transaction mode.</summary>
			<param name="station">The printer station to be used. Possible values are defined by the <see cref="T:Microsoft.PointOfService.PrinterStation"></see> enumeration. Valid values are: PrinterStation.Journal, PrinterStation.Receipt, or PrinterStation.Slip.</param>
			<param name="control">Transaction control. Possible values are defined by the <see cref="T:Microsoft.PointOfService.PrinterTransactionControl"></see> enumeration.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.TransactionPrintImpl(Microsoft.PointOfService.BaseServiceObjects.PrintOperationCollection)">
			<summary>Method implemented by service objects to process TransactionPrint requests.</summary>
			<param name="collection">The collection of transaction print requests.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ValidateData(Microsoft.PointOfService.PrinterStation,System.String)">
			<summary>Validates data for a specified printer station.</summary>
			<param name="data">The data that will be validated for the specified <see cref="T:Microsoft.PointOfService.PrinterStation"></see>.</param>
			<param name="station">The <see cref="T:Microsoft.PointOfService.PrinterStation"></see> for which the data will be validated.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ValidateDataImpl(Microsoft.PointOfService.PrinterStation,System.String)">
			<summary>Method implemented by the service object to process ValidateData requests.</summary>
			<param name="data">The data that will be validated for the specified <see cref="T:Microsoft.PointOfService.PrinterStation"></see>.</param>
			<param name="station">The <see cref="T:Microsoft.PointOfService.PrinterStation"></see> for which the data will be validated.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.AsyncMode">
			<summary>Indicates whether certain print methods will be performed asynchronously. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapCharacterSet">
			<summary>Holds the default character set capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapConcurrentJrnRec">
			<summary>Indicates whether Journal and receipt stations can print at the same time.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapConcurrentJrnSlp">
			<summary>Indicates whether journal and slip stations can print at the same time.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapConcurrentPageMode">
			<summary>Indicates whether the printer is capable of supporting page mode for the receipt and slip stations concurrently.</summary>
			<returns>A boolean indicating whether the printer is capable of supporting page mode for the receipt and slip stations concurrently.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapConcurrentRecSlp">
			<summary>Indicates whether receipt and slip stations can print at the same time.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapCoverSensor">
			<summary>Indicates whether the printer has a “cover open” sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrn2Color">
			<summary>Indicates whether the journal can print dark plus an alternate color.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnBold">
			<summary>Indicates whether the journal can print bold characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnCartridgeSensor">
			<summary>Indicates the presence of journal cartridge monitoring sensors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnColor">
			<summary>Indicates available journal color cartridges.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnDHigh">
			<summary>Indicates whether the journal can print double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnDWide">
			<summary>Indicates whether the journal can print double-wide characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnDWideDHigh">
			<summary>Indicates whether the journal can print double-wide and double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnEmptySensor">
			<summary>Indicates whether the journal has an out-of-paper sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnItalic">
			<summary>Indicates whether the journal can print italic characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnNearEndSensor">
			<summary>Indicates whether the journal has a low-paper sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnPresent">
			<summary>Indicates whether the journal print station is present.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapJrnUnderline">
			<summary>Indicates whether the journal can underline characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapMapCharacterSet">
			<summary>Indicates that the service object is able to map the characters sent my the application to a character set defined by the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CharacterSetList"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRec2Color">
			<summary>Indicates whether the receipt can print dark plus an alternate color.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecBarCode">
			<summary>Indicates whether the receipt has bar code printing capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecBitmap">
			<summary>Indicates whether the receipt can print bitmaps.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecBold">
			<summary>Indicates whether the receipt can print bold text.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecCartridgeSensor">
			<summary>Indicates the presence of receipt cartridge monitoring sensors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecColor">
			<summary>Indicates the receipt color cartridges.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecDHigh">
			<summary>Indicates whether the receipt can print double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecDWide">
			<summary>Indicates whether the receipt can print double-wide characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecDWideDHigh">
			<summary>Indicates whether the receipt can print double-wide, double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecEmptySensor">
			<summary>Indicates whether the receipt print station has an out-of-paper sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecItalic">
			<summary>Indicates whether the receipt can print italic text.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecLeft90">
			<summary>Indicates whether the receipt can print in rotated-90-degree-left mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecMarkFeed">
			<summary>Holds the type of mark-sensed paper handling available.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecNearEndSensor">
			<summary>Indicates whether the receipt has a low-paper sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecPaperCut">
			<summary>Indicates whether the receipt can perform paper cuts.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecPresent">
			<summary>Indicates that the receipt print station is present.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecRight90">
			<summary>Indicates that the receipt can print in a rotated-90-degree-right mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecRotate180">
			<summary>Indicates that the receipt can print in a rotated-180-degree mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecStamp">
			<summary>Indicates that the receipt has a stamp capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapRecUnderline">
			<summary>Indicates whether the receipt can underline characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlp2Color">
			<summary>Indicates that the slip can print dark plus an alternate color.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpBarCode">
			<summary>Indicates that the slip has bar code printing capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpBitmap">
			<summary>Indicates that the slip can print bitmaps.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpBold">
			<summary>Indicates that the slip can print bold text.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpBothSidesPrint">
			<summary>Indicates that the slip print station supports printing on both sides of the document.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpCartridgeSensor">
			<summary>Indicates the presence of slip-cartridge monitoring sensors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpColor">
			<summary>Indicates which types of color cartridges supported for slip printing.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpDHigh">
			<summary>Indicates that the slip can print double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpDWide">
			<summary>Indicates that the slip can print double-wide characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpDWideDHigh">
			<summary>Indicates that the slip can print double-wide, double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpEmptySensor">
			<summary>Indicates that the slip has a “slip in” sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpFullSlip">
			<summary>Indicates whether the printer supports printing full-length forms on the slip station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpItalic">
			<summary>Indicates that the slip can print italic characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpLeft90">
			<summary>Indicates that the slip can print in a rotated 90 degree left mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpNearEndSensor">
			<summary>Indicates whether the slip has a "slip near end" sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpPageMode">
			<summary>Indicates whether the printer is capable of supporting Page Mode for the slip station.</summary>
			<returns>A boolean indicating whether the printer is capable of supporting Page Mode for the slip station.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpPresent">
			<summary>Indicates that the printer has a slip station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpRight90">
			<summary>Indicates that the slip can print in a rotated 90 degree right mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpRotate180">
			<summary>Indicates that the slip can print in a rotated 180 degree mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapSlpUnderline">
			<summary>Indicates that the slip can print underlined characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CapTransaction">
			<summary>Indicates whether or not each station supports printer transactions.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CartridgeNotify">
			<summary>Holds the type of cartridge state notification the application wants to receive.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CharacterSet">
			<summary>Holds a numeric value indicating the character set the application wants to use for printing characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CharacterSetList">
			<summary>Holds a list of character set numbers supported for printing.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CoverOpen">
			<summary>Indicates the cover is open.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.DeviceEnabled">
			<summary>Gets or sets a Boolean value that indicates whether the device has been placed in an operational state.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ErrorLevel">
			<summary>Holds the severity of the most recent error condition.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ErrorStation">
			<summary>Holds the station or stations that were printing when an error was detected.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.ErrorString">
			<summary>Holds a vendor-supplied description of the current error.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.FlagWhenIdle">
			<summary>If set to true by the application, the service object will queue a <see cref="StatusUpdateEvent"></see> event using the <see cref="StatusIdle"></see> constant when the device is in an idle state, and reset <see cref="FlagWhenIdle"></see> to false.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.FontTypefaceList">
			<summary>Holds a string array that specifies the fonts and/or typefaces supported by the printer.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnCartridgeState">
			<summary>Holds the status of the selected journal cartridge (ink, ribbon, or toner).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnCurrentCartridge">
			<summary>Holds the currently selected journal cartridge.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnEmpty">
			<summary>If set to true, the journal is out of paper.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnLineChars">
			<summary>Holds the number of characters that the application wants to print on a journal line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnLineCharsList">
			<summary>Holds a collection of the line widths (characters per line) supported by the journal station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnLineHeight">
			<summary>Holds the journal print line height the application wants to use, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnLineWidth">
			<summary>Holds the width of a line for the number of characters indicated by the <see cref="JrnLineChars"></see> property, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.JrnNearEnd">
			<summary>If true, the journal paper is low.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MapCharacterSet">
			<summary>If set to true by the application while outputting data, the service object maps the characters transferred by the application to the character set selected in the <see cref="CharacterSet"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MapMode">
			<summary>Holds the mapping mode of the printer the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeArea">
			<summary>Holds the page area for the selected <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeStation"></see> expressed in the unit of measure given by <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MapMode"></see>.</summary>
			<returns>A point representing the total page area for the selected <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeStation"></see>.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeDescriptor">
			<summary>Holds a value that indicates the level of basic Page Mode functionality for the printer.</summary>
			<returns>A <see cref="T:Microsoft.PointOfService.PageModeDescriptors"></see> value indicating the level of basic Page Mode functionality.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeHorizontalPosition">
			<summary>Holds the horizontal start position offset within the print area for the selected <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.PageModeStation"></see>, expressed in the unit of measure given by <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MapMode"></see>.</summary>
			<returns>In integer that holds the horizontal start position offset within the print area for the selected <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.PageModeStation"></see>.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModePrintArea">
			<summary>Holds the print area for the selected <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeStation"></see> expressed in the unit of measure given by <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.MapMode"></see>.</summary>
			<returns>A Rectangle indicating the print area.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModePrintDirection">
			<summary>Holds the print direction.</summary>
			<returns>A <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModePrintDirection"></see> value that indicates the print direction.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeStation">
			<summary>Gets and sets the print station for use by other Page Mode properties</summary>
			<returns>A <see cref="T:Microsoft.PointOfService.PrinterStation"></see> value that indicates the current printer mode.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeVerticalPosition">
			<summary>Holds the vertical start position offset within the print area for the selected <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PageModeStation"></see>.</summary>
			<returns>An integer representing the vertical start position offset within the print area.</returns>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.Properties">
			<summary>Returns a class of writable properties that can be used to modify the read-only properties used by applications.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecBarCodeRotationList">
			<summary>Holds a list of the directions in which a receipt bar code can be rotated.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecBitmapRotationList">
			<summary>Holds a list of the directions in which a receipt bitmap can be rotated.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecCartridgeState">
			<summary>Holds the status of the selected receipt cartridge (ink, ribbon or toner).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecCurrentCartridge">
			<summary>Holds the receipt cartridge currently selected by the application.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecEmpty">
			<summary>If true, the receipt is out of paper.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecLineChars">
			<summary>Holds the number of characters that the application wants to print on a receipt line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecLineCharsList">
			<summary>Holds a collection of the line widths (characters per line) supported by the receipt station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecLineHeight">
			<summary>Holds the receipt print line height, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecLinesToPaperCut">
			<summary>Holds the number of lines that must be advanced before cutting the receipt paper.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecLineWidth">
			<summary>Holds the width of a line, based on the number of characters specified in the <see cref="RecLineChars"></see> property, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecNearEnd">
			<summary>If true, the receipt paper is low.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecSidewaysMaxChars">
			<summary>Holds the maximum number of characters that can be printed on each line in sideways mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RecSidewaysMaxLines">
			<summary>Holds the maximum number of lines that can be printed in sideways mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RotateSpecial">
			<summary>Holds the rotation orientation for bar codes that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpBarCodeRotationList">
			<summary>Holds a collection of the directions in which a slip bar code may be rotated.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpBitmapRotationList">
			<summary>Holds a collection of the directions in which a slip bitmap can be rotated.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpCartridgeState">
			<summary>Holds the status of the selected slip cartridge (ink, ribbon, or toner).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpCurrentCartridge">
			<summary>Holds the slip cartridge the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpEmpty">
			<summary>If true, a slip form is not present.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpLineChars">
			<summary>Holds the number of characters that the application wants to print on a slip line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpLineCharsList">
			<summary>Holds a collection of the line widths (characters per line) supported by the slip station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpLineHeight">
			<summary>Holds the slip print-line height, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpLinesNearEndToEnd">
			<summary>Holds the number of lines that can be printed after the <see cref="SlpNearEnd"></see> property has been set to true but before the printer reaches the end of the slip.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpLineWidth">
			<summary>Holds the line width, based on the number of characters specified for the <see cref="SlpLineChars"></see> property, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpMaxLines">
			<summary>Holds the maximum number of lines that can be printed on a form.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpNearEnd">
			<summary>If true, the slip form is near its end.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpPrintSide">
			<summary>Holds the side of the slip print station document on which printing will occur.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpSidewaysMaxChars">
			<summary>Holds the maximum number of characters that can be printed on each line in sideways mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.SlpSidewaysMaxLines">
			<summary>Holds the maximum number of lines that can be printed in sideways mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.StopCurrentOutput">
			<summary>Indicates whether output is stopped.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.UseExternalPrintQueue">
			<summary>Indicates whether an external print queue is used.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CancelPrintEventHandler">
			<summary>Delegate for the <see cref="E:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CancelPrintEvent"></see> event. </summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrintBarCodeOperation">
			<summary>Captures information about a print operation method call.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBarCodeOperation.Alignment">
			<summary>Contains the value of the alignment parameter set when the application called the <see cref="PrintBarCode"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBarCodeOperation.Data">
			<summary>Contains the value of the data parameter set when the application called the <see cref="PrintBarCode"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBarCodeOperation.Height">
			<summary>Contains the value of the height parameter set when the application called the <see cref="PrintBarCode"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBarCodeOperation.Symbology">
			<summary>Contains the value of the symbology parameter set when the application called the <see cref="PrintBarCode"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBarCodeOperation.TextPosition">
			<summary>Contains the value of the textPosition parameter set when the application called the <see cref="PrintBarCode"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBarCodeOperation.Width">
			<summary>Contains the value of the width parameter set when the application called the <see cref="PrintBarCode"></see> method.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrintBitmapOperation">
			<summary>Captures information about the <see cref="M:Microsoft.PointOfService.PosPrinter.PrintBitmap(Microsoft.PointOfService.PrinterStation,System.String,System.Int32,System.Int32)"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBitmapOperation.Alignment">
			<summary>Contains the value of the alignment parameter set when the application called the <see cref="PrintBitmap"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBitmapOperation.FileName">
			<summary>Contains the value of the filename parameter set when the application called the <see cref="PrintBitmap"></see> method.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintBitmapOperation.Width">
			<summary>Contains the values of the width parameter set when the application called the <see cref="PrintBitmap"></see> method.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties">
			<summary>A helper class that simplifies the retrieval and setting of property values for printer devices.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.AsyncMode">
			<summary>Indicates whether certain print methods will be performed asynchronously. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.BottomLogo">
			<summary>Determines whether to print a logo during print operations.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapCharacterSet">
			<summary>Holds the default character set capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapConcurrentJrnRec">
			<summary>Indicates whether Journal and receipt stations can print at the same time.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapConcurrentJrnSlp">
			<summary>Indicates whether journal and slip stations can print at the same time.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapConcurrentRecSlp">
			<summary>Indicates whether receipt and slip stations can print at the same time.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapCoverSensor">
			<summary>Indicates whether the printer has a “cover open” sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrn2Color">
			<summary>Indicates whether the journal can print dark plus an alternate color.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnBold">
			<summary>Indicates whether the journal can print bold characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnCartridgeSensor">
			<summary>Indicates the presence of journal cartridge monitoring sensors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnColor">
			<summary>Indicates available journal color cartridges.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnDHigh">
			<summary>Indicates whether the journal can print double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnDWide">
			<summary>Indicates whether the journal can print double-wide characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnDWideDHigh">
			<summary>Indicates whether the journal can print double-wide and double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnEmptySensor">
			<summary>Indicates whether the journal has an out-of-paper sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnItalic">
			<summary>Indicates whether the journal can print italic characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnNearEndSensor">
			<summary>Indicates whether the journal has a low-paper sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnPresent">
			<summary>Indicates whether the journal print station is present.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapJrnUnderline">
			<summary>Indicates whether the journal can underline characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapMapCharacterSet">
			<summary>Indicates that the service object is able to map the characters sent my the application to a character set defined by the <see cref="P:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.CharacterSetList"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRec2Color">
			<summary>Indicates whether the receipt can print dark plus an alternate color.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecBarCode">
			<summary>Indicates whether the receipt has bar code printing capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecBitmap">
			<summary>Indicates whether the receipt can print bitmaps.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecBold">
			<summary>Indicates whether the receipt can print bold text.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecCartridgeSensor">
			<summary>Indicates the presence of receipt cartridge monitoring sensors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecColor">
			<summary>Indicates the receipt color cartridges.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecDHigh">
			<summary>Indicates whether the receipt can print double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecDWide">
			<summary>Indicates whether the receipt can print double-wide characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecDWideDHigh">
			<summary>Indicates whether the receipt can print double-wide, double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecEmptySensor">
			<summary>Indicates whether the receipt print station has an out-of-paper sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecItalic">
			<summary>Indicates whether the receipt can print italic text.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecLeft90">
			<summary>Indicates whether the receipt can print in rotated-90-degree-left mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecMarkFeed">
			<summary>Holds the type of mark-sensed paper handling available.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecNearEndSensor">
			<summary>Indicates whether the receipt has a low-paper sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecPaperCut">
			<summary>Indicates whether the receipt can perform paper cuts.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecPresent">
			<summary>Indicates that the receipt print station is present.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecRight90">
			<summary>Indicates that the receipt can print in a rotated-90-degree-right mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecRotate180">
			<summary>Indicates that the receipt can print in a rotated-180-degree mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecStamp">
			<summary>Indicates that the receipt has a stamp capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapRecUnderline">
			<summary>Indicates whether the receipt can underline characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlp2Color">
			<summary>Indicates that the slip can print dark plus an alternate color.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpBarCode">
			<summary>Indicates that the slip has bar code printing capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpBitmap">
			<summary>Indicates that the slip can print bitmaps.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpBold">
			<summary>Indicates that the slip can print bold text.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpBothSidesPrint">
			<summary>Indicates that the slip print station supports printing on both sides of the document.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpCartridgeSensor">
			<summary>Indicates the presence of slip-cartridge monitoring sensors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpColor">
			<summary>Indicates which types of color cartridges supported for slip printing.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpDHigh">
			<summary>Indicates that the slip can print double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpDWide">
			<summary>Indicates that the slip can print double-wide characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpDWideDHigh">
			<summary>Indicates that the slip can print double-wide, double-high characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpEmptySensor">
			<summary>Indicates that the slip has a “slip in” sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpFullSlip">
			<summary>Indicates whether the printer supports printing full-length forms on the slip station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpItalic">
			<summary>Indicates that the slip can print italic characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpLeft90">
			<summary>Indicates that the slip can print in a rotated 90 degree left mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpNearEndSensor">
			<summary>Indicates whether the slip has a "slip near end" sensor.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpPresent">
			<summary>Indicates that the printer has a slip station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpRight90">
			<summary>Indicates that the slip can print in a rotated 90 degree right mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpRotate180">
			<summary>Indicates that the slip can print in a rotated 180 degree mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapSlpUnderline">
			<summary>Indicates that the slip can print underlined characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CapTransaction">
			<summary>Indicates whether or not each station supports printer transactions.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CartridgeNotify">
			<summary>Holds the type of cartridge state notification the application wants to receive.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CharacterSet">
			<summary>Holds a numeric value indicating the character set the application wants to use for printing characters.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CharacterSetList">
			<summary>Holds a list of character set numbers supported for printing.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.CoverOpen">
			<summary>Indicates that the cover is open.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.ErrorLevel">
			<summary>Holds the severity of the most recent error condition.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.ErrorStation">
			<summary>Holds the station or stations that were printing when an error was detected.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.ErrorString">
			<summary>Holds a vendor-supplied description of the current error.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.FlagWhenIdle">
			<summary>If set to true by the application, the service object will queue a <see cref="StatusUpdateEvent"></see> event using the <see cref="StatusIdle"></see> constant when the device is in an idle state, and reset <see cref="FlagWhenIdle"></see> to false.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.FontTypefaceList">
			<summary>Holds a string array that specifies the fonts and/or typefaces supported by the printer.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnCartridgeState">
			<summary>Holds the status of the selected journal cartridge (ink, ribbon, or toner).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnCoverOpen">
			<summary>Notifies the application that the cover has been opened.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnCurrentCartridge">
			<summary>Holds the currently selected journal cartridge.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnEmpty">
			<summary>If set to true, the journal is out of paper.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnLineChars">
			<summary>Holds the number of characters that the application wants to print on a journal line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnLineCharsList">
			<summary>Holds a collection of the line widths (characters per line) supported by the journal station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnLineHeight">
			<summary>Holds the journal print line height the application wants to use, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnLineWidth">
			<summary>Holds the width of a line for the number of characters indicated by the <see cref="JrnLineChars"></see> property, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.JrnNearEnd">
			<summary>If true, the journal paper is low.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.MapCharacterSet">
			<summary>If set to true by the application while outputting data, the service object maps the characters transferred by the application to the character set selected in the <see cref="CharacterSet"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.MapMode">
			<summary>Holds the mapping mode of the printer the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecBarCodeRotationList">
			<summary>Holds a list of the directions in which a receipt bar code can be rotated.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecBitmapRotationList">
			<summary>Holds a list of the directions in which a receipt bitmap can be rotated.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecCartridgeState">
			<summary>Holds the status of the selected receipt cartridge (ink, ribbon or toner).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecCoverOpen">
			<summary>Indicates that a cover has been opened.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecCurrentCartridge">
			<summary>Holds the receipt cartridge currently selected by the application.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecEmpty">
			<summary>If true, the receipt is out of paper.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecLineChars">
			<summary>Holds the number of characters that the application wants to print on a receipt line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecLineCharsList">
			<summary>Holds a collection of the line widths (characters per line) supported by the receipt station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecLineHeight">
			<summary>Holds the receipt print line height, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecLinesToPaperCut">
			<summary>Holds the number of lines that must be advanced before cutting the receipt paper.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecLineWidth">
			<summary>Holds the width of a line, based on the number of characters specified in the <see cref="RecLineChars"></see> property, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecNearEnd">
			<summary>If true, the receipt paper is low.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecSidewaysMaxChars">
			<summary>Holds the maximum number of characters that can be printed on each line in sideways mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RecSidewaysMaxLines">
			<summary>Holds the maximum number of lines that can be printed in sideways mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.RotateSpecial">
			<summary>Holds the rotation orientation for bar codes that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpBarCodeRotationList">
			<summary>Holds a collection of the directions in which a slip bar code may be rotated.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpBitmapRotationList">
			<summary>Holds a collection of the directions in which a slip bitmap can be rotated.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpCartridgeState">
			<summary>Holds the status of the selected slip cartridge (ink, ribbon, or toner).</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpCoverOpen">
			<summary>Indicates that a cover has been opened.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpCurrentCartridge">
			<summary>Holds the slip cartridge the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpEmpty">
			<summary>If true, a slip form is not present.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpLineChars">
			<summary>Holds the number of characters that the application wants to print on a slip line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpLineCharsList">
			<summary>Holds a collection of the line widths (characters per line) supported by the slip station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpLineHeight">
			<summary>Holds the slip print-line height, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpLinesNearEndToEnd">
			<summary>Holds the number of lines that can be printed after the <see cref="SlpNearEnd"></see> property has been set to true but before the printer reaches the end of the slip.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpLineWidth">
			<summary>Holds the line width, based on the number of characters specified for the <see cref="SlpLineChars"></see> property, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpMaxLines">
			<summary>Holds the maximum number of lines that can be printed on a form.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpNearEnd">
			<summary>If true, the slip form is near its end.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpPrintSide">
			<summary>Holds the side of the slip print station document on which printing will occur.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpSidewaysMaxChars">
			<summary>Holds the maximum number of characters that can be printed on each line in sideways mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.SlpSidewaysMaxLines">
			<summary>Holds the maximum number of lines that can be printed in sideways mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterProperties.TopLogo">
			<summary>Holds a value indicating whether a logo should be printed during print operations.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrinterState">
			<summary>Contains property values holding information about the printer state.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.BottomLogo">
			<summary>Determines whether to print a logo during print operations.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.CharacterSet">
			<summary>Holds the default character set capability.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.JrnCurrentCartridge">
			<summary>Holds the currently selected journal cartridge.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.JrnLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.JrnLineChars">
			<summary>Holds the number of characters that the application wants to print on a journal line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.JrnLineHeight">
			<summary>Holds the journal print line height the application wants to use, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.JrnLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.MapCharacterSet">
			<summary>If set to true by the application while outputting data, the service object maps the characters transferred by the application to the character set selected in the <see cref="CharacterSet"></see> property.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.MapMode">
			<summary>Holds the mapping mode of the printer the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.RecCurrentCartridge">
			<summary>Holds the receipt cartridge currently selected by the application.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.RecLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.RecLineChars">
			<summary>Holds the number of characters that the application wants to print on a receipt line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.RecLineHeight">
			<summary>Holds the receipt print line height, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.RecLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.RotateSpecial">
			<summary>Holds the rotation orientation for bar codes that the application wants to use. </summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.SlpCurrentCartridge">
			<summary>Holds the slip cartridge the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.SlpLetterQuality">
			<summary>If true, prints in high-quality mode. If false, prints in high-speed mode.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.SlpLineChars">
			<summary>Holds the number of characters that the application wants to print on a slip line.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.SlpLineHeight">
			<summary>Holds the slip print-line height, expressed in the unit of measure indicated by the <see cref="MapMode"></see> property setting, that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.SlpLineSpacing">
			<summary>Holds the spacing of each single-high print line, including both the printed line height and the white space between each pair of lines that the application wants to use.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrinterState.TopLogo">
			<summary>Holds a value indicating whether a logo should be printed during print operations.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrintMemoryBitmapOperation">
			<summary>Captures information about the <see cref="M:Microsoft.PointOfService.PosPrinter.PrintMemoryBitmap(Microsoft.PointOfService.PrinterStation,System.Drawing.Bitmap,System.Int32,System.Int32)"></see> method.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrintNormalOperation">
			<summary>Capture and holds information about a print method call.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintNormalOperation.Data">
			<summary>Contains the value of the data parameter set when the application made the <see cref="PrintNormal"></see> method call.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrintOperation">
			<summary>The abstract class definition from which all other <see cref="&lt;PrintMethodName&gt;Operation"></see> classes are derived.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintOperation.AsyncMode">
			<summary>Contains the <see cref="AsyncMode"></see> property setting at the time of the application’s print request.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintOperation.Rotation">
			<summary>Contains the <see cref="Rotation"></see> setting, as defined by the <see cref="Rotation"></see> enumeration.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintOperation.State">
			<summary>Contains an instance of the <see cref="PrinterState"></see> class, which captures the current printer property values at the time of the application’s print request.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintOperation.Station">
			<summary>Contains the printer station setting (for example, <see cref="PrinterStation.Journal"></see>).</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrintOperationCollection">
			<summary>Maintains a list of print operations and their associated state information.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PrintOperationCollection.CopyTo(Microsoft.PointOfService.BaseServiceObjects.PrintOperation[],System.Int32)">
			<summary>Copies a <see cref="PrintOperation"></see> class instance to the array.</summary>
			<param name="array">The array of <see cref="PrintOperation"></see> class instances.</param>
			<param name="index">The index number into the array.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintOperationCollection.Item(System.Int32)">
			<summary>Retrieves an instance of the <see cref="PrintOperation"></see> class from the array.</summary>
			<param name="index">The index number for the <see cref="PrintOperation"></see> class instance to be retrieved.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintOperationCollection.Station">
			<summary>Retrieves the printer station, as defined by the <see cref="PrinterStation"></see> enumeration, associated with the array.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrintResults">
			<summary>The <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PrintResults"></see> helper class simplifies the return of status from service object-implemented base class methods and is used by the <see cref="PosPrinterBase"></see> class to maintain and update printer statistics.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.PrintResults.#ctor">
			<summary>Creates a new instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PrintResults"></see> class.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.BarcodePrintedCount">
			<summary>Contains the number of barcodes printed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.ErrorCode">
			<summary>Contains the error code for the current error condition.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.ErrorCodeExtended">
			<summary>Contains the extended error code for the current error condition.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.ErrorLevel">
			<summary>Contains the current error level for the printer device.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.ErrorString">
			<summary>Contains a vendor-supplied description of the error that has occurred.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.FailedPaperCutCount">
			<summary>Contains the number of failed paper cuts.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.FailedPrintSideChangeCount">
			<summary>Contains the number of print side changes (or check flips) failures.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.FormInsertionCount">
			<summary>Contains the number of forms inserted into the document/slip station.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.HomeErrorCount">
			<summary>Contains the number of home errors.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.JournalCharacterPrintedCount">
			<summary>Contains the number of Journal characters printed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.JournalLinePrintedCount">
			<summary>Contains the number of Journal lines printed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.MaximumTempReachedCount">
			<summary>Contains the number of times Maximum temperature was reached.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.NVRAMWriteCount">
			<summary>Contains the number of times NVRAM is written to.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.PaperCutCount">
			<summary>Contains the number of paper cuts.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.PrinterFaultCount">
			<summary>Contains the number of printer faults.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.PrinterStation">
			<summary>Contains the current printer station, as defined by the <see cref="PrinterStation"></see> enumeration.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.PrintSideChangeCount">
			<summary>Contains the number of print side changes (or check flips) performed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.ReceiptCharacterPrintedCount">
			<summary>Contains the number of receipt characters printed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.ReceiptLineFeedCount">
			<summary>Contains the number of receipt line feeds performed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.ReceiptLinePrintedCount">
			<summary>Contains the number of receipt lines printed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.SlipCharacterPrintedCount">
			<summary>Contains the number of document/slip characters printed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.SlipLineFeedCount">
			<summary>Contains the number of document/slip line feeds performed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.SlipLinePrintedCount">
			<summary>Contains the number of document/slip lines printed.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintResults.StampFiredCount">
			<summary>Contains the number of Stamps fired.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.PrintTwoNormalOperation">
			<summary>The <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PrintTwoNormalOperation"></see> helper class is used by <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase"></see> to capture information about the <see cref="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.PrintTwoNormal(Microsoft.PointOfService.PrinterStation,System.String,System.String)"></see> method call.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintTwoNormalOperation.Data1">
			<summary>Contains the value of the data1 parameter set when the application made the <see cref="PrintTwoNormal"></see> method call.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.PrintTwoNormalOperation.Data2">
			<summary>Contains the value of the data2 parameter set when the application made the <see cref="PrintTwoNormal"></see> method call.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.RFIDDataEventArgs">
			<summary>Defines the programmatic interface for the transmission of RFID data events.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDDataEventArgs.#ctor(System.Collections.Generic.ICollection{Microsoft.PointOfService.BaseServiceObjects.RFIDTag})">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDDataEventArgs"></see> class by using the currentTags parameter.</summary>
			<param name="currentTags">A list of currentTags.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDDataEventArgs.#ctor(Microsoft.PointOfService.BaseServiceObjects.RFIDTag)">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDDataEventArgs"></see> class by using the tag parameter.</summary>
			<param name="tag">The tag that contains the ID and userData.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDDataEventArgs.#ctor(System.Byte[],System.Byte[],System.DateTime)">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDDataEventArgs"></see> class by using the id, data, and time parameters.</summary>
			<param name="time">The time when the tag was read.</param>
			<param name="data">The user data contained in the tag.</param>
			<param name="id">The tag ID.</param>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase">
			<summary>Contains most of the functionality for support of RFID Scanner devices.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.#ctor">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.CheckHealth(Microsoft.PointOfService.HealthCheckLevel)">
			<summary>Indicates the current health status of the device.</summary>
			<param name="level">The type of health check to be performed on the device.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.ClearInputProperties">
			<summary>Clears all data properties and sets them to their default values.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.FirstTag">
			<summary>Resets the service’s counter to the first tag in the tag list, and copies that tag’s information into the corresponding properties.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.NextTag">
			<summary>Moves the service’s counter to the next tag in the tag list, and copies that tag’s information into the corresponding properties.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.Open">
			<summary>Opens a device for later input/output processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.PreFireEvent(Microsoft.PointOfService.DataEventArgs)">
			<summary>Called prior to the delivery of a <see cref="DataEvent"></see> event to the application.</summary>
			<param name="posEvent">An instance of the <see cref="DataEventArgs"></see> class, posEvent contains the event that was queued to the application.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.PreviousTag">
			<summary>Moves the service’s counter to the previous tag in the tag list, and copies that tag’s information into the corresponding properties.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.ReadRFIDTags(Microsoft.PointOfService.RFIDReadOptions,System.Int32,System.Int32,System.Byte[])">
			<summary>Performs a poll of all tags within range that meet the parameterized criteria.</summary>
			<param name="start">Indicates the zero-based position within the tags UserData field to begin reading from.</param>
			<param name="length">Indicates the number of bytes of user data to read starting at the position indicated by the start parameter.</param>
			<param name="password">Authorized key for reader that might be required for this operation, null if not applicable.</param>
			<param name="option">Indicates the enumerated read option from <see cref="T:Microsoft.PointOfService.RFIDReadOptions"></see>.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.ReadTags(Microsoft.PointOfService.RFIDReadOptions,System.Byte[],System.Byte[],System.Int32,System.Int32,System.Int32,System.Byte[])">
			<summary>Begins a poll of all the tags within range that meet the parameterized criteria.</summary>
			<param name="start">Indicates the zero-based position within the tags UserData field to begin reading from.</param>
			<param name="filterMask">Holds a value which is used to mask the filterID. If the bitwise AND matches the bitwise AND of a tagId and the filterMask, the tagId will be returned. To get all tags in the field, pass in a filterMask of 0.</param>
			<param name="filterId">Holds a bit pattern to be AND-ed with filterMask to determine which tag(s) to read.</param>
			<param name="length">Indicates the number of bytes of user data to read starting at the position indicated by the start parameter.</param>
			<param name="password">Authorized key for reader that might be required for this operation, null if not applicable.</param>
			<param name="timeout">Allowed execution time, in milliseconds, before the method fails and a time-out occurs.</param>
			<param name="cmd">Optional command parameters.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.StartReadTags(Microsoft.PointOfService.RFIDReadOptions,System.Byte[],System.Byte[],System.Int32,System.Int32,System.Byte[])">
			<summary>Performs a continuous polling of tags that meet the parameterized criteria.</summary>
			<param name="start">Indicates the zero-based position within the tags UserData field to begin reading from.</param>
			<param name="filterMask">Holds a value which is used to mask the filterID. If the bitwise AND matches the bitwise AND of a tagId and the filterMask, the tagId will be returned. To get all tags in the field, pass in a filterMask of 0.</param>
			<param name="filterId">Holds a bit pattern to be AND-ed with filterMask to determine which tag(s) to read.</param>
			<param name="length">Indicates the number of bytes of user data to read starting at the position indicated by the start parameter.</param>
			<param name="password">Authorized key for reader that might be required for this operation, null if not applicable.</param>
			<param name="cmd">Optional command parameters.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.StopReadTags(System.Byte[])">
			<summary>Stops the Continuous Read mode.</summary>
			<param name="password">Authorized key for reader that might be required for this operation, null if not applicable.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.WriteTagData(System.Byte[],System.Byte[],System.Int32,System.Int32,System.Byte[])">
			<summary>Overwrites the entire or part of the userData field of targeted tag.</summary>
			<param name="start">Indicates the zero-based position within the tags userData field to begin writing to.</param>
			<param name="tagId">ID of the tag whose user data it is writing to.</param>
			<param name="password">Authorized key for reader that might be required for this operation, null if not applicable.</param>
			<param name="timeout">Allowed execution time, in milliseconds, before the method fails and a timeout occurs.</param>
			<param name="userData">User data to be written.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase.WriteTagDataImpl(System.Byte[],System.Byte[],System.Int32,System.Int32,System.Byte[],System.DateTime)">
			<summary>Overwrites all or partial user data contained in the tag.</summary>
			<param name="start">Indicates the zero-based position within the tags userData field to begin writing to.</param>
			<param name="tagId">ID of the tag whose user data it is writing to.</param>
			<param name="password">Authorized key for reader that might be required for this operation, null if not applicable.</param>
			<param name="startTime">Indicates the time to start writing data to the tag.</param>
			<param name="timeout">Allowed execution time, in milliseconds, before the method fails and a timeout occurs.</param>
			<param name="userData">User data to be written.</param>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerProperties">
			<summary>Defines the common properties of the RFID Scanner device.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.RFIDTag">
			<summary>A helper class created by POS for .NET and used by <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDScannerBase"></see> class for handling RFID tags.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDTag.#ctor">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDTag"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDTag.#ctor(System.Byte[])">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDTag"></see> class and initializes the id parameter.</summary>
			<param name="id">The tag ID.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDTag.#ctor(Microsoft.PointOfService.BaseServiceObjects.RFIDTag)">
			<summary>Creates a new instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDTag"></see> class and initializes it with the fields from the specified RFIDTag.</summary>
			<param name="tag">The data in the tag's fields.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDTag.#ctor(System.Byte[],System.Byte[])">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDTag"></see> class and initializes the id and data parameters.</summary>
			<param name="data">The user data contained in the tag.</param>
			<param name="id">The tag ID.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDTag.#ctor(System.Byte[],System.DateTime)">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDTag"></see> class and initializes the id and time parameters.</summary>
			<param name="time">The time when the tag was read.</param>
			<param name="id">The tag ID.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.RFIDTag.#ctor(System.Byte[],System.Byte[],System.DateTime)">
			<summary>Creates an instance of the <see cref="T:Microsoft.PointOfService.BaseServiceObjects.RFIDTag"></see> class and initializes the id, data, and time parameters.</summary>
			<param name="time">The time when the tag was read.</param>
			<param name="data">The user data contained in the tag.</param>
			<param name="id">The tag ID.</param>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.RotatePrintCollection">
			<summary>The <see cref="RotatePrintCollection"></see> helper class is used to store an array, or list, of requested rotation print operations.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.RotatePrintCollection.Rotation">
			<summary>Contains the rotation mode.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.RotatePrintOperation">
			<summary>The <see cref="RotatePrintOperation"></see> helper class is used by <see cref="T:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase"></see> to capture the requested rotation whenever the <see cref="M:Microsoft.PointOfService.BaseServiceObjects.PosPrinterBase.RotatePrint(Microsoft.PointOfService.PrinterStation,Microsoft.PointOfService.PrintRotation)"></see> method is called.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.RotatePrintOperation.Collection">
			<summary>Contains a collection, which is an instance of the <see cref="RotatePrintCollection"></see> class.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.ScannerBase">
			<summary>Contains most of the functionality for support of scanner devices.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.#ctor">
			<summary>Creates an instance of the <see cref="ScannerBase"></see> class.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.ClearInputProperties">
			<summary>Resets the values of properties altered by <see cref="E:Microsoft.PointOfService.Scanner.DataEvent"></see> or <see cref="E:Microsoft.PointOfService.Scanner.ErrorEvent"></see>.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.DecodeScanDataLabel(System.Byte[])">
			<summary>A default implementation of <see cref="DecodeScanDataLabel"></see> that returns an empty byte array.</summary>
			<param name="scanData">The scanData parameter contains the scanned data, from which the service object will extract the data label information.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.DecodeScanDataType(System.Byte[])">
			<summary>A default implementation that returns a value of <see cref="BarCodeSymbology.Unknown"></see>.</summary>
			<param name="scanData">The scanData parameter contains the scanned data from which the service object will extract the data type information.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.FailedScan">
			<summary>Called by the service object to have <see cref="ScannerBase"></see> class notify the application that the current scan has failed.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.GoodScan(System.Byte[])">
			<summary>Called by the service object to request that <see cref="ScannerBase"></see> class queue a data event containing the scanned data to the application.</summary>
			<param name="scanData">Contains the scanned data.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.GoodScan(System.Byte[],Microsoft.PointOfService.BarCodeSymbology,System.Byte[])">
			<summary>Called by the service object to request that <see cref="ScannerBase"></see> class queue a data event containing the scanned data to the application.</summary>
			<param name="scanDataType">Contains the decoded bar code label type.</param>
			<param name="scanData">Contains the scanned data.</param>
			<param name="scanDataLabel">Contains the decoded bar code data.</param>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.Open">
			<summary>Opens a device for later input/output processing.</summary>
		</member>
		<member name="M:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.PreFireEvent(Microsoft.PointOfService.DataEventArgs)">
			<summary>Called prior to the delivery of a <see cref="DataEvent"></see> event to the application.</summary>
			<param name="posEvent">An instance of the <see cref="DataEventArgs"></see> class, posEvent contains the event that was queued to the application.</param>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.DecodeData">
			<summary>When the application sets <see cref="DecodeData"></see> to true, the service object decodes the value of the <see cref="ScanData"></see> property and places the decoded data in the <see cref="ScanDataLabel"></see> and <see cref="ScanDataType"></see> properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.Properties">
			<summary>Contains getters and setters for common and scanner device properties.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.ScanData">
			<summary>Holds a byte array of the data read from the scanner.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.ScanDataLabel">
			<summary>Holds the decoded bar code label.</summary>
		</member>
		<member name="P:Microsoft.PointOfService.BaseServiceObjects.ScannerBase.ScanDataType">
			<summary>Holds the decoded bar code label type. Possible values are defined by the <see cref="BarCodeSymbology"></see> enumeration.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.ScannerProperties">
			<summary>The <see cref="T:Microsoft.PointOfService.BaseServiceObjects.ScannerProperties"></see> helper class simplifies the setting and retrieval of scanner device property values.</summary>
		</member>
		<member name="T:Microsoft.PointOfService.BaseServiceObjects.SetStatistic">
			<summary>Sets the specified statistic to the specified value.</summary>
		</member>
	</members>
</doc>