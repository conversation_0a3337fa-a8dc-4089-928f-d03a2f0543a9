﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
StoreManager
</name>
</assembly>
<members>
<member name="T:StoreManager.POSDataSet.Report_Daily_SaleDataTable">
	<summary>
Represents the strongly named DataTable class.
</summary>
</member><member name="T:StoreManager.POSDataSet.Report_Shift_SaleDataTable">
	<summary>
Represents the strongly named DataTable class.
</summary>
</member><member name="T:StoreManager.POSDataSet.Report_Daily_SaleRow">
	<summary>
Represents strongly named DataRow class.
</summary>
</member><member name="T:StoreManager.POSDataSet.Report_Shift_SaleRow">
	<summary>
Represents strongly named DataRow class.
</summary>
</member><member name="T:StoreManager.POSDataSet.Report_Daily_SaleRowChangeEvent">
	<summary>
Row event argument class
</summary>
</member><member name="T:StoreManager.POSDataSet.Report_Shift_SaleRowChangeEvent">
	<summary>
Row event argument class
</summary>
</member><member name="T:StoreManager.POSDataSet">
	<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member><member name="T:StoreManager.POSDataSetTableAdapters.Report_Daily_SaleTableAdapter">
	<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member><member name="T:StoreManager.POSDataSetTableAdapters.Report_Shift_SaleTableAdapter">
	<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member><member name="M:StoreManager.POSDataSetTableAdapters.TableAdapterManager.UpdateUpdatedRows(StoreManager.POSDataSet,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
	<summary>
Update rows in top-down order.
</summary>
</member><member name="M:StoreManager.POSDataSetTableAdapters.TableAdapterManager.UpdateInsertedRows(StoreManager.POSDataSet,System.Collections.Generic.List{System.Data.DataRow})">
	<summary>
Insert rows in top-down order.
</summary>
</member><member name="M:StoreManager.POSDataSetTableAdapters.TableAdapterManager.UpdateDeletedRows(StoreManager.POSDataSet,System.Collections.Generic.List{System.Data.DataRow})">
	<summary>
Delete rows in bottom-up order.
</summary>
</member><member name="M:StoreManager.POSDataSetTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
	<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member><member name="M:StoreManager.POSDataSetTableAdapters.TableAdapterManager.UpdateAll(StoreManager.POSDataSet)">
	<summary>
Update all changes to the dataset.
</summary>
</member><member name="T:StoreManager.POSDataSetTableAdapters.TableAdapterManager.UpdateOrderOption">
	<summary>
Update Order Option
</summary>
</member><member name="T:StoreManager.POSDataSetTableAdapters.TableAdapterManager.SelfReferenceComparer">
	<summary>
Used to sort self-referenced table's rows
</summary>
</member><member name="T:StoreManager.POSDataSetTableAdapters.TableAdapterManager">
	<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member><member name="P:StoreManager.My.Resources.Resources.ResourceManager">
	<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member><member name="P:StoreManager.My.Resources.Resources.Culture">
	<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member><member name="T:StoreManager.My.Resources.Resources">
	<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
</members>
</doc>