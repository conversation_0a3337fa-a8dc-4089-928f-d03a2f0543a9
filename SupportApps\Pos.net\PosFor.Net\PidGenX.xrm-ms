﻿<?xml version="1.0" encoding="utf-8"?><rg:licenseGroup xmlns:rg="urn:mpeg:mpeg21:2003:01-REL-R-NS"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" licenseId="{c0071a6f-894c-4346-927c-fb83afbd4a75}" xmlns:sx="urn:mpeg:mpeg21:2003:01-REL-SX-NS" xmlns:mx="urn:mpeg:mpeg21:2003:01-REL-MX-NS" xmlns:sl="http://www.microsoft.com/DRM/XrML2/SL/v2" xmlns:tm="http://www.microsoft.com/DRM/XrML2/TM/v2"><r:title>XrML 2.1 License - Product Key Configuration</r:title><r:issuer><Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.microsoft.com/xrml/lwc14n"/><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/><Reference><Transforms><Transform Algorithm="urn:mpeg:mpeg21:2003:01-REL-R-NS:licenseTransform"/><Transform Algorithm="http://www.microsoft.com/xrml/lwc14n"/></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/><DigestValue>E1EqFjSPRSD/SiMfLZOD0Hhme8w=</DigestValue></Reference></SignedInfo><SignatureValue>md6dSwEyndKQboZO9pYc2tzzDXgR0xm7Fe9YiBLLD/8Gj39YvlHZEsbPRvA5TCD1Qlf+l0SLFuB5q2wOsO2b1ZLMOv85vqdFa89mRPofNcGsFR4Xpb2SZeKCsloFsa7pewMNH22B984VS0v6VT/9b5rYn2kUUF/NSL4aE0lJaD8ad1+2H3N7eCTiFJBLSugUdizh4J6r79U17A5hXeEdCTvDV52OFcEUcE3RSLadjGZ35TM0rUuEdk7KeONAz5tyumUbVj5s45tPwNG2oA7njbF0rryh6gUgcSRMVPb5D4MIETn1X8WOe6KWSbeGkq6ubebu2CPIaet7Et/xwSysug==</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>1N+QaYteSIjGmRMzTkxCE+5oiPoLk2Fq+RA9GLnl+dHOcyxt2a/0HvUdagaL/NwDquzOef4JOMMuVavd4PtWQiO/aBLvxVv7yIhUhhB6PEsw59mhbVlT/Z5OGkp6gfzH9ezZ+qHHFHo0cloAAu5QGUeuYCPLheVK7X3+syHE1qXagfRa5m0xG+770FyPeMKazK+keeQ/goW+nt2wTM9Pofj4yTGCbn6Fc6EpKdyHmzrzQDc5FjZemXP2PbGjS6iPC7l3+Ut5JPL66ZUZzCs5qRc+/wRODknUWAcqURJWP79knfPhf3/dvbytHpr64wFfpBNDSbNVubol0E8oTa/NYw==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue></KeyInfo></Signature></r:issuer><r:otherInfo xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS"><tm:infoTables xmlns:tm="http://www.microsoft.com/DRM/XrML2/TM/v2"><tm:infoList tag="#global"><tm:infoStr name="licenseType">msft:sl/PKEYCONFIG/SIGNED</tm:infoStr><tm:infoStr name="licenseCategory">msft:sl/PKEYCONFIG/SIGNED</tm:infoStr><tm:infoStr name="licenseVersion">2.0</tm:infoStr><tm:infoBin name="pkeyConfigData">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</tm:infoBin></tm:infoList></tm:infoTables></r:otherInfo></r:license></rg:licenseGroup>